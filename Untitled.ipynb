{"cells": [{"cell_type": "code", "execution_count": 9, "id": "4711b1fa-4692-4de9-8f82-8ee6f9087cad", "metadata": {}, "outputs": [], "source": ["from langchain_ollama import OllamaLLM\n", "from langchain_core.messages import HumanMessage, AIMessage\n", "from langchain_ollama import ChatOllama\n", "from langchain_ollama import OllamaEmbeddings\n", "from langchain_core.vectorstores import InMemoryVectorStore\n", "\n", "\n", "from langchain import hub\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_core.documents import Document\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "from langgraph.graph import START, StateGraph\n", "\n", "import os\n", "import bs4\n", "from typing_extensions import List, TypedDict"]}, {"cell_type": "code", "execution_count": 2, "id": "5d82eb78-8e09-41c1-9bc4-5a74161266c4", "metadata": {}, "outputs": [], "source": ["os.environ['LANGSMITH_TRACING']=\"true\"\n", "os.environ['LANGSMITH_ENDPOINT']=\"https://api.smith.langchain.com\"\n", "os.environ['LANGSMITH_API_KEY']=\"***************************************************\"\n", "os.environ['LANGSMITH_PROJECT']=\"rag1\""]}, {"cell_type": "code", "execution_count": 5, "id": "8e7d83cd-dc30-4b40-952e-0028499df09b", "metadata": {}, "outputs": [], "source": ["llm = OllamaLLM(model=\"llama3.2\")\n", "chat_model = ChatOllama(model=\"llama3.2\")\n", "embeddings = OllamaEmbeddings(model=\"llama3.2\")"]}, {"cell_type": "code", "execution_count": 6, "id": "1a69dc4f-4259-4be4-ac26-f770304064d6", "metadata": {}, "outputs": [], "source": ["vector_store = InMemoryVectorStore(embeddings)"]}, {"cell_type": "code", "execution_count": 10, "id": "e3861ae4-67f4-4aad-96eb-b79f243d69e9", "metadata": {}, "outputs": [], "source": ["loader = WebBaseLoader(\n", "    web_paths=(\"https://lilianweng.github.io/posts/2023-06-23-agent/\",),\n", "    bs_kwargs=dict(\n", "        parse_only=bs4.SoupStrainer(\n", "            class_=(\"post-content\", \"post-title\", \"post-header\")\n", "        )\n", "    ),\n", ")\n", "docs = loader.load()"]}, {"cell_type": "code", "execution_count": 11, "id": "c6faa14b-9ffc-424d-aa84-58dd88cb12e8", "metadata": {}, "outputs": [], "source": ["text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)\n", "all_splits = text_splitter.split_documents(docs)"]}, {"cell_type": "code", "execution_count": 14, "id": "f68d0161-3717-435f-a307-5a25ce8d791b", "metadata": {"scrolled": true}, "outputs": [], "source": ["_ = vector_store.add_documents(documents=all_splits)"]}, {"cell_type": "code", "execution_count": 23, "id": "91619b12-1ecc-4d90-ab9d-f32012e3abe6", "metadata": {}, "outputs": [], "source": ["class State(TypedDict):\n", "    question: str\n", "    context: List[Document]\n", "    answer: str\n", "\n", "\n", "# Define application steps\n", "def retrieve(state: State):\n", "    retrieved_docs = vector_store.similarity_search(state[\"question\"])\n", "    return {\"context\": retrieved_docs}\n", "\n", "\n", "def generate(state: State):\n", "    docs_content = \"\\n\\n\".join(doc.page_content for doc in state[\"context\"])\n", "    messages = prompt.invoke({\"question\": state[\"question\"], \"context\": docs_content})\n", "    response = llm.invoke(messages)\n", "    print(response)\n", "    return {\"answer\": response}"]}, {"cell_type": "code", "execution_count": 24, "id": "18cf2ddd-e1fb-4e44-8bdc-1a8676a8f161", "metadata": {}, "outputs": [], "source": ["prompt = hub.pull(\"rlm/rag-prompt\")\n", "\n", "graph_builder = StateGraph(State).add_sequence([retrieve, generate])\n", "graph_builder.add_edge(START, \"retrieve\")\n", "graph = graph_builder.compile()"]}, {"cell_type": "code", "execution_count": 25, "id": "ce392074-4530-434c-80c3-51ffac37bd05", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Task Decomposition is a technique where an agent breaks down complex tasks into smaller, simpler steps. This process allows models to utilize more computation and provide insights into their thinking process. The Chain of Thought (CoT) prompting technique is used for task decomposition, instructing the model to \"think step by step\".\n"]}], "source": ["response = graph.invoke({\"question\": \"What is Task Decomposition?\"})"]}, {"cell_type": "code", "execution_count": 2, "id": "c71675b6-b1a6-48d4-a206-9fd45d6f2ee0", "metadata": {}, "outputs": [], "source": ["\n", "x = '''\n", "So this opens up a huge possibility. This could be an extension of the Chinese economy,” he added.\n", "This highly offensive statement is not only insensitive and undiplomatic, it is deliberate interference in the internal matters of India. <PERSON><PERSON> does not hold an elected post in the interim government of Dhaka, nor is he competent to advise Beijing on how to conduct its economy in the region. His mandate, if any, is limited to the revival of the economy of Bangladesh and guiding it back on the path of democracy. The affairs in the country were derailed by an illegitimate uprising in 2024 by Islamic radicals under the guise of <PERSON><PERSON> (ICS), the student wing of Jamaat-e-Islami, aided and abated by the ISI of Pakistan.\n", "Enemies turn brothers\n", "'''"]}, {"cell_type": "code", "execution_count": 4, "id": "477de4fb-c854-45eb-884a-5ffca81dfe61", "metadata": {}, "outputs": [{"data": {"text/plain": ["760"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["len(x)"]}, {"cell_type": "code", "execution_count": 6, "id": "42caa3ca-32cd-41c1-a2ca-20ff01838e1e", "metadata": {}, "outputs": [], "source": ["import json\n", "d= json.load(open(\"PrintScraper/output/theprint_india_20250406_133109.json\"))"]}, {"cell_type": "code", "execution_count": 11, "id": "e6a5f721-1e7b-4ddb-8894-445e3b7c9c63", "metadata": {}, "outputs": [{"data": {"text/plain": ["5365"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["len(d[0]['content'])"]}, {"cell_type": "code", "execution_count": 32, "id": "519db5a9-e18a-4d89-bd7b-b7c07c44b922", "metadata": {}, "outputs": [], "source": ["import ollama\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 33, "id": "43846e98-8ce2-46ab-854a-d1d3d8456a7d", "metadata": {}, "outputs": [], "source": ["def sim_score(a,b):\n", "    return np.dot(a, b)/(np.linalg.norm(a)*np.linalg.norm(b))"]}, {"cell_type": "code", "execution_count": 60, "id": "7fe43af0-e7ab-4c2f-8566-527e8199c359", "metadata": {}, "outputs": [{"data": {"text/plain": ["'New Delhi: At 82, Sardar <PERSON><PERSON> may struggle to recall names, but he never forgets a face. He has been stitching military uniforms since he was 15.<PERSON>’s journey from the small village of Jagdev Kalan in Amritsar district to becoming a trusted name among India’s military personnel, including Field Marshal <PERSON> whose birth anniversary falls on 3 April, is one of resilience and dedication.<PERSON> arrived in Delhi at the age of 12, just after India gained independence. Poverty drove him to leave his village, with a large family to support and siblings to marry off. “I had completed my studies up to class 5 before moving here,” he recalled while speaking to ThePrint.He first stayed with an elder sister in Delhi Cantonment for three years before being placed under the care of <PERSON><PERSON><PERSON><PERSON> and <PERSON>rda<PERSON><PERSON> (93), family acquaintances from Punjab. At the age of 15, he began his apprenticeship under the <PERSON><PERSON> brothers, who taught him the trade. Their shop, now known as The Battle Fatigues Store, was earlier called Madras Cloth House and Popular Tailors. “They showed me the ropes, and my journey has been closely intertwined with theirs,” said <PERSON>.Over the years, <PERSON> has stitched uniforms for former Army chiefs, including Field Marshal <PERSON>, General <PERSON><PERSON><PERSON>, and General <PERSON><PERSON><PERSON>, who retired only last year.He recalled recognising most officers from their early years in service, even if he can’t remember their names.Given <PERSON>’s legacy, there are, however, others who claim to have stitched his uniforms in the same market. Only a few shops away sits a competitor by the name of Tailloo Ram, which, having opened in 1932, claims to be the oldest shop in the market. The owner of Tai<PERSON>o <PERSON> claims it was their shop which stitched General <PERSON>ekshaw’s clothing.<PERSON>flecting on his early struggles, <PERSON> said tailoring was more than a job—it was a necessity. “Work was a compulsion. There weren’t many opportunities in the village, and this trade had value, though it demanded effort,” he said. Tailoring in his early days involved intricate handwork, unlike today’s machine-aided methods. “Now, it’s easier, but back then, everything was done by hand.”His dedication hasn’t gone unnoticed. Many retired officers still visit him, including a general who came to the shop recently after decades. “The moment he saw me, his eyes lit up,” Singh recalled with pride.For Singh, it’s the relationships formed through his work that bring him joy. “Customers I have known for 30-50 years still come back. They have grown old, like me, but seeing each other brings happiness,” he said.He fondly remembered an air force pilot who likened their respective trades. “The pilot told me, ‘What aircraft is to us, the cloth you cut is to you. If you knew its value, you would never be able to cut it.’”Singh credits his mentor, Sardar Surjeet Singh Bawa, for his enduring success and continues to learn from him. “Even at 82, I consider myself a student. Tayaji, now 93, is still my guide,” he said.The family’s tailoring business has seen its share of glory. During the late 1990s and early 2000s, they stitched 1,500 uniforms daily for military units, including those deployed on UN missions. They also catered to production houses, with actor Shah Rukh Khan visiting the store during the filming of Fauji.Singh has five children—two daughters and three sons. While the daughters are married, the sons have pursued careers in electrical work, tailoring, and transportation. He never aspired for his children to join the military, saying, “They didn’t study enough for it.”(Edited by Radifah Kabir)Also Read: Complex network of India’s existing air defence capabilities & the way forward'"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["art['content'].replace(\"\\n\", \"\")"]}, {"cell_type": "code", "execution_count": 61, "id": "b10553bc-875a-4459-b516-1e8d14666551", "metadata": {}, "outputs": [], "source": ["vs = []\n", "for art in d:\n", "    vs.append(np.array(ollama.embed(model='llama3.2', input=art['content'].replace(\"\\n\", \"\")).embeddings))"]}, {"cell_type": "code", "execution_count": 30, "id": "b5b5adba-616c-44bc-95d3-c8b62d12e482", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 64, "id": "ed5379a9-2aa7-47da-a775-f128c682dfb7", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'title': '‘I’m not a BJP person, nor do I subscribe to its ideology,’ <PERSON><PERSON><PERSON> said in 2021',\n", " 'url': 'https://theprint.in/opinion/im-not-a-bjp-person-nor-do-i-subscribe-to-its-ideology-manoj-kumar-said-in-2021/2579388/',\n", " 'author': '<PERSON>',\n", " 'date': '2025-04-05',\n", " 'content': 'Mumbai: Four years ago I was doing my research for a book on the Hindi film industry veterans. <PERSON><PERSON><PERSON> was, of course, one of them.\\nBeing a film journalist and critic for nearly three decades, I had access to most contacts—from <PERSON> (<PERSON><PERSON>) to <PERSON><PERSON><PERSON>’s daughter, <PERSON><PERSON><PERSON>, who was a regular visitor to <PERSON><PERSON>’s office in Mumbai.\\nI called up <PERSON><PERSON><PERSON>’s personal number. As luck would have it, he picked it up. I was lucky as his calls were mostly filtered by a caretaker at home.\\n“Nam<PERSON><PERSON>. <PERSON><PERSON><PERSON> speaking”. I was pleasantly surprised. His voice was sturdy, thick, flawless, and sophisticated with no sign of any ailment. And I knew he wasn’t well enough to meet people.\\n“Adab!”, I said as I introduced myself and expressed my wish to interview him. He paused for a while and asked in Hindi, “Kya aap mujhe television camera par record karengi?” (Would you record me on TV Camera?) “No! on dicta-phone. I mean a tape recorder,” I replied.\\n“Theek hai to phir. Tashreef laiye. Khairmaqdam! “ (Fine then. Welcome home).\\n<PERSON> was duly impressed by his response in Urdu. I had called him from Delhi.\\nCut to Mumbai. He lived in Goswami Towers. The society, huge but unpretentious, didn’t even look like an abode of celebrated actors.\\n<PERSON> was accompanied by a friend – an All India Radio Bureau Chief <PERSON> (English). He wanted to meet <PERSON> to ask him if he could record for AIR someday too.\\n<PERSON> rang the bell and the door opened instantly. “<PERSON> <PERSON>am?. <PERSON>ayiye, <PERSON> aapka intezar kar rahe hain.” (He is waiting for you) <PERSON> smiling caretaker led me in, joined by <PERSON>’s wife <PERSON>shila Gos<PERSON>—a writer and a school teacher. <PERSON> simple woman in a simpler salwar suit, with a smile on her face.\\n<PERSON> was escorted to his room. <PERSON> I entered, I was in for a shock. Kumar was lying on his bed, surrounded by a score of newspapers he was reading and keeping aside. The room had a stench – of a patient perhaps being cleaned frequently after using a bedpan.\\nThe bedsheet hadn’t been changed for some time, it seemed. It would have taken stronger hands to lift him for that. I knew this, having cared for my father who was critically ill. It is excruciatingly painful for someone whose back has almost given up, as Kumar’s had in those days.\\nHis wife must have sensed my shock. She said, “We don’t trouble this child anymore”.\\n“I love you”, Kumar threw a flying kiss to her, and told me, “She is my best buddy, in the absence of all so-called buddies in the film industry.”\\nI couldn’t ask him who visits him. He answered anyway, “They call up, but kabhi kabhi (infrequently), you know” and laughed like he had accepted it as his fortune for the times.\\nI noticed that all the newspapers on his bed were in Urdu. “So you read Urdu?” I asked.\\n“Arrey Bilkul! I read all Urdu newspapers,” he said and started naming them one by one. “Kya sheerin hai is zaban mein” (This language is sweet).” And he broke into a couplet by Habeeb Ahmad applauding the language: Taaliim-o-tarbiyat mein aham is ka hai maqaam\\nSher-o-sukhan ki rooh adab ki ye jaan hai\\n(This language is prime for education and culture/ it is the soul of poetry and literature)\\nThe mood was set.\\nA traditionalist\\nI decided not to make things formal. “So, a Harikrishna Giri Goswami becomes Bharat Kumar in Hindi films and becomes synonymous with Indian nationalism?”\\nHe laughed like I told him a joke. “Actually, Hindi film world typecasts you instantly. I did a few roles of a patriot and I became a Bharat Kumar. Well, isn’t it a beautiful name? So I thought, let it be. But I think I was great as a romantic hero, no?” He looked at us for validation.\\nWe laughed and said, “Of course!” My friend said, “You were the most good-looking and handsome hero of your time. Even heroines had to be beautiful to match up to your good looks.”\\nHe laughed and said, “Thank You. I actually liked myself on screen.” The room was filled with pleasant chirps. The stench was not noticeable. We were served tea and biscuits.\\nWe spoke of the hit films that he directed—Upkar (1967), Purab aur Paschim (1970), Roti, Kapda Aur Makaan (1974), Kranti (1981) and more. He got hooked on Purab aur Paschim.\\n“Let me tell you something about the role Saira Banu played in Purab aur Paschim,” he said, turning slightly to my side. I was seated on his left, on the bed itself, while my friend was in the centre, on a chair. Kumar rested on his arms, helped by the caretaker who placed two bolsters behind his back for comfort.\\nThe seed for Purab and Paschim was sown when Kumar went to a filmi party, and saw a young girl who looked very comfortable in her short clothes was smoking like a pro.\\n“ I am a traditionalist and I love Indian culture, and our dresses especially women’s attire – sari and shalwar suit. I kept on observing her manners and by the time I was back home, a film on Indian and Western culture was ready on my mind,” he said, “Now I had to look for that face which would fit that girl’s. After some screen shots, I requested Saira Banu who has sharp features, fair complexion, and was young and wouldn’t look unnatural with those blonde hair wigs and equally classic in Indian saris. For Saira, it was a challenge that she handled so well. You know that the film was a bumper hit?”\\n“Yes, parents used to show that film to their young children to move away from extreme westernisation. So did ours,” I said.\\nAlso read: Manoj ‘Bharat’ Kumar sang so Sunny Deol could yell—journey of Bollywood patriotism\\n‘I am not political’\\nI was struggling to mention politics to him.\\n“You were born in Pakistan (1937) you came to India. How do you see India-Pak relations or Hindu-Muslim shared co-existence in India?” I asked.\\n“Hamari Ganga Jamni tehzzeeb ka jawab nahi. We are one. Our film industry is a live example. My film had Pakistan’s Dilip Kumar (Mohammad Ali) and did that role without money, in the name of our friendship.” And he broke into this sher by Kanwal Zia:\\nHamara Khoon ka rishta hai Sarhadon ka nahi\\nHamare khoon mein Ganga bhi Chanab bhi hai\\n(We are bonded by blood, not boundaries/ My blood has waters of both Ganga and Chanab (rivers).\\nI asked, plainly: “But you joined the BJP, which clearly declares that it hates minorities, especially Muslims.\\n“Who said? Never! I am not a BJP person or belong to its ideology at all. One day, they came to me. Gave a lot of respect. Made me wear an orange colour safa (turban) and a stole. For me, orange is a colour of Sufism and sacrifice,” he said, “What are you saying? I actually was overwhelmed by their love and respect. They felicitated me. Took me to some meetings and that’s about it.”\\n“I never gave any speech in favour of the party anywhere. I was not aware of any agenda they had. For them, I was a symbol of patriotism, Bharat—the nation, and that is because my films portray me as a nationalist. I am one but in its true way, not agenda-wise,” he nearly started choking with emotions. His eyes went moist.\\nI was apologetic. I extended a glass of water. He sipped a few drops and said, “Rana jee. Khuda ki qasam, ye politics wale hum film walon ko, hamari image ko misuse karte hain. Mera unsey koi political raabta nahi.” (I swear, these politics people misuse people from the film industry and their image. I don’t have anything to do with them)\\n“Did you mind me asking this?” I was worried on account of his health. “No no. I just wanted to clarify. Aap media wale hain na. Isko phaila dijiye. (You are media people, spread my opinion).\\nTo pacify him, I recited this to him:\\nAb to mazhab koi aisa chalaya jaye\\nJis mein insaan ko insaan banaya jaye.\\n“Right! Ye sher Neeraj (Gopal Das Neeraj) ka hai. I like it.” He had recognised the poet immediately.\\nBefore I took his leave, I asked if I could take a picture with him.\\n“In this condition? Let’s not do it. Achcha nahi lagta. People see me as a strong man. This could break their heart. I will feel bad too,” he said, and I respected it.\\nOn asking if I could visit him again, he said I was welcome as many times as I wished.\\n“ I loved talking heart with you,” he said with folded hands. All this while, he was lying down—not a trace of pain on his face.\\nBut I feel the pain, because I know I shall never be able to meet him again.\\nI must share that after a couple more calls, his family requested that he wasn’t keeping too well to talk or meet. So I stopped calling.\\nRana Siddiqui Zaman is a film and art journalist for 28 years. Views are personal.\\n(Edited by Ratan Priya)\\nThe clickbait headline is indicative of the quality of journalism done by Ms. Zaman.\\nHer insistence on communicating via Urdu and her blatantly communal world view gets amply reflected in this article.'}"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["query = \"story near the LOC between india and pakistan and about army and soldiers\"\n", "query_emb = np.array(ollama.embed(model='llama3.2', input=query).embeddings)\n", "scores = []\n", "for v in vs:\n", "    scores.append(sim_score(query_emb.reshape(-1),v.reshape(-1)))\n", "scores = np.array(scores)\n", "d[np.a<PERSON><PERSON>(scores)]"]}, {"cell_type": "code", "execution_count": 62, "id": "f8d7e976-51f7-4a57-8e2c-68c32ab1aaec", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'title': 'India, Pakistan are sleepwalking toward another crisis on the LOC',\n", " 'url': 'https://theprint.in/opinion/india-pakistan-are-sleepwalking-toward-another-crisis-on-the-loc/2579929/',\n", " 'author': '<PERSON><PERSON><PERSON>',\n", " 'date': '2025-04-06',\n", " 'content': 'Lieutenant-Colonel <PERSON><PERSON><PERSON> had tried to stall his incredulous headquarters when the orders came in to attack, claiming 13 December 1971 was an inauspicious date. The excuse had been manufactured on the fly, but it turned out to be prophetic. From an old mule shed on the mountain, ill-prepared soldiers desperately clawed their way up the path toward the summit at Daruchhian, their progress marked by blood-stained clods of earth. Five officers, seven junior commissioned officers, and 18 other soldiers were killed—with another 74 missing in action—before commanders called off the attack.\\nLast week, bitter fighting erupted again around that 1971 battleground. Troops positioned at the Indian Army posts—Nangi Tekri, Jungle Tekri and Bump, all captured in hand-to-hand combat during the lead-up to the battle at Darucchian—opened fire across the Line of Control in the most serious incident since a ceasefire was put in place in 2021.\\nThere is no clarity on exactly what happened. The Army rolled back its early claim that there had been an intrusion across the Line of Control but asserted that its troops had responded appropriately to a provocation. For its part, the Pakistan Army stated that it began a patrol on its side of the Line, which was hit by the accidental explosion of an old landmine, killing <PERSON><PERSON><PERSON><PERSON>.\\nLike so often in the past, India and Pakistan might just be walking toward another crisis on the LOC, oblivious of their missteps and misjudgments of the path.\\nFor weeks now, tensions have been mounting in the region as jihadists from Pakistan-occupied Kashmir have pushed their way past the LOC. Two Indian soldiers were killed near Akhnoor last month by an Improvised Explosive Device (IED) believed to have been planted on their patrol route by a jihadist unit. Islamabad, for its part, has alleged that the Indian Army has been planting IEDs targeting its soldiers and said two of its soldiers have been injured in gunfire.\\nEven violence in Kashmir has dipped, with killings last year falling to the lowest levels seen since 2012, Islamabad’s tone is hardening. “Three wars have been fought for Kashmir, and if ten more need to be fought, we will fight,” declared Pakistan’s army chief General Asim Munir at a Kashmir solidarity event in February. Last month, Indian diplomatic sources say, General Munir also told a closed-door session of Pakistan’s Parliamentary Committee on National Security that India was fuelling violence in Balochistan.\\nFrom Kargil to crisis management\\nLessons on the need to maintain peace on the LOC were learned by both India and Pakistan under the shadow of nuclear weapons. Following the two countries’ nuclear weapons tests in 1998, then-Army chief General Pervez Musharraf went to war in Kargil. General Musharraf believed—rightly, it turned out—that India would be deterred by the risk of a nuclear war from widening the conflict. Even though he lost in Kargil, he continued to ratchet up the pain. From 1999 to 2003, India lost a staggering 2,125 security force personnel in jihadist attacks—four times the number killed in Kargil.\\nThe Jaish-e-Muhammad attack on Parliament in 2001 convinced Prime Minister Atal Bihari Vajpayee that he needed to use coercive means to contain the threat. Vajpayee ordered the Army to position itself for war, leading to what has been described as the most significant military mobilisation since World War II. Then, in 2003, PM Vajpayee seemed to reverse course, announcing New Delhi was unilaterally “opening the door for talks”.\\nEven though experts have argued the 2001-2002 stand-off failed to secure its aims because of Pakistan’s nuclear deterrence—a proposition borne out of continued violence in Kashmir—the story is more complex. India did appear to blink first but General Musharraf’s advisors had concluded that the crisis was bleeding Pakistan more than it was hurting New Delhi.\\nLieutenant-General Moinuddin Haider, General Musharraf’s interior minister, told the scholar George Perkovich that he had explained to his boss: “Mr President, your economic plan will not work; people will not invest if you don’t get rid of extremists.” The President, albeit reluctantly, listened, violence data from Kashmir shows. Fatalities fell year-on-year as the Inter-Services Intelligence Directorate quietly choked off jihadist clients like the Jaish and Lashkar-e-Taiba.\\nUnsigned notes revealed in 2009 show that secret envoys working for Prime Minister Manmohan Singh and General Musharraf came close to a final-status deal on Kashmir. The negotiations, begun under Prime Minister Vajpayee by then-R&AW chief CD Sahay and ISI director Lieutenant-General Ehsan-ul-Haq, envisaged the LOC being turned into a border, with a high degree of freedom of movement and autonomy on both sides.\\nAlso read: Pakistan doesn’t have enough troops for 2 fronts. It has to choose between LOC, Balochistan\\nGeneral Kayani’s new war\\nHowever, the rise of General Pervez Ashfaq Kayani as Pakistan’s new army chief in 2008 saw this process unravel. In 2008, the US was reported to have confronted Pakistan’s army with evidence that the ISI was involved in a suicide attack on the Indian diplomatic mission in Kabul. Later that year came the carnage in Mumbai. Former Central Intelligence Agency chief Michael Hayden has recorded he was confident the ISI was involved in the planning and execution of the 2008 Mumbai attacks but failed to compel it to act against the perpetrators.\\nFor all practical purposes, General Kayani had dismantled the peace process that had started in the wake of the 2001-2002 crisis. Violence in Kashmir started to grow again. The LOC grew increasingly volatile as the Pakistan Army opened fire to facilitate infiltrating groups of jihadists. The two armies became locked in a cycle of increasingly brutal raids and counter-attacks, sometimes leading to the beheadings of soldiers.\\nThe killing of 17 Indian soldiers in Uri by the Lashkar-e-Taiba led Prime Minister Narendra Modi to order strikes across the Line of Control—an attack that sought to reimpose deterrence against Pakistan’s use of terrorism, but without locking India into a 2001-2002-like crisis. The damage caused by the strikes was minimal, but New Delhi hoped to show Islamabad it was willing to go to war if terrorism continued to escalate.\\nISI commanders were coerced into ending terrorist strikes outside Kashmir but stepped up violence within the state by using Fidayeen suicide attackers. This laid the foundation of three years of terrorism and Line of Control clashes, culminating in India’s bombing of a Jaish-e-Muhammad base in Pulwama.\\nBajwa’s long stalemate\\nFollowing the 2019 crisis, both sides pulled back from the edge. Like in 2003, R&AW and the ISI prepared the ground for negotiations, with Major-General Isfandiyar Pataudi and R Kumar meeting in secret at a London hotel. Few details have emerged on the negotiations, but their talks prepared the ground for the ceasefire in 2021. Like General Musharraf, General Qamar Javed Bajwa concluded that a crisis with India offered no real benefits to an economically devastated Pakistan and could only escalate into a full-blown war.\\nThe most crucial problem, though, is the absence of a constituency for India-Pakistan peace. As the scholar Christopher Clary has thoughtfully observed, political leaders remain wary of resuming talks. Modi and his cabinet have shown little interest in reopening dialogue. For his part, Prime Minister Shehbaz Sharif rejected even a limited reopening of trade with India in 2022, saying “genocide is going on there, and Kashmiris have been denied their rights.” Instead, Pakistani policymakers insist that dialogue must include Kashmir—something New Delhi has shot down.\\nEvents on the Line of Control last week, though, make clear that time isn’t on the side of either country. Even the limited terrorist attacks seen on the Indian Army and Hindu residents of Kashmir in 2024 could potentially have led to results that would have compelled New Delhi to strike across the border. The skirmishes on the Line of Control, similarly, can spiral, just like they did in the build-up after 1999, 2008, and 2016.\\nGeneral Munir’s words suggest he no longer sees a stalemate on the Line of Control as serving Pakistan’s interests. The calibrated use of violence might seem like leverage to him, but his predecessors learned that it’s only a small step from there to plunging the region into crisis.\\nPraveen Swami is contributing editor at ThePrint. His X handle is @praveenswami. Views are personal.\\n(Edited by Ratan Priya)\\nA tragic reminder that even instincts dismissed as superstition can echo the heavy cost of war.'}"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["query = \"story about <PERSON><PERSON><PERSON>\"\n", "query_emb = np.array(ollama.embed(model='llama3.2', input=query).embeddings)\n", "scores = []\n", "for v in vs:\n", "    scores.append(sim_score(query_emb.reshape(-1),v.reshape(-1)))\n", "scores = np.array(scores)\n", "d[np.a<PERSON><PERSON>(scores)]"]}, {"cell_type": "code", "execution_count": null, "id": "caf41f7d-b00a-432a-ad02-51705b4be191", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 51, "id": "9ada09fa-f107-4796-9cab-fb04fac511ff", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.21096967, 0.20615141, 0.21144065, 0.22011306, 0.21761578,\n", "       0.20769033, 0.21414354, 0.21466752, 0.22014009, 0.20732671,\n", "       0.20246263, 0.20755208, 0.21463491, 0.20823335, 0.24607524,\n", "       0.34409038, 0.42577998, 0.21096967, 0.20615141, 0.21144065,\n", "       0.22011306, 0.20732671, 0.20246263, 0.20755208, 0.20722607,\n", "       0.20510518, 0.21128924, 0.22715455, 0.20527507, 0.20305154,\n", "       0.21463491, 0.20823335, 0.24607524, 0.34409038, 0.42577998,\n", "       0.22011306, 0.22539511, 0.2094118 , 0.21910094, 0.20387287,\n", "       0.21792892, 0.21766109, 0.21922454, 0.21392692, 0.21244986,\n", "       0.22464316, 0.21557801, 0.21252145, 0.24607524, 0.25846693,\n", "       0.21003517, 0.34409038, 0.42577998, 0.22011306, 0.22539511,\n", "       0.2094118 , 0.21910094, 0.21043765, 0.21885459, 0.21911881,\n", "       0.2167125 , 0.2044851 , 0.21020788, 0.20346566, 0.2110469 ,\n", "       0.20314861, 0.2924174 , 0.21394856, 0.22736684, 0.34409038,\n", "       0.42577998, 0.22011306, 0.22539511, 0.2094118 , 0.21910094,\n", "       0.21869702, 0.21235635, 0.20061418, 0.20887377, 0.21043765,\n", "       0.21885459, 0.21911881, 0.2167125 , 0.2044851 , 0.21020788,\n", "       0.21003517, 0.2924174 , 0.21394856, 0.34409038, 0.42577998,\n", "       0.22011306, 0.22539511, 0.2094118 , 0.21910094, 0.20713155,\n", "       0.21419173, 0.19965277, 0.21837938, 0.20323476, 0.21672773,\n", "       0.21090932, 0.21973517, 0.20536028, 0.20818928, 0.2924174 ,\n", "       0.21394856, 0.22736684, 0.34409038, 0.42577998, 0.20615141,\n", "       0.21144065, 0.22011306, 0.22539511, 0.21700384, 0.2117185 ,\n", "       0.2164564 , 0.21869702, 0.21235635, 0.20061418, 0.20887377,\n", "       0.21043765, 0.21885459, 0.21911881, 0.21463491, 0.20823335,\n", "       0.24607524, 0.34409038, 0.42577998, 0.22011306, 0.22539511,\n", "       0.2094118 , 0.21910094, 0.21232525, 0.20713155, 0.21419173,\n", "       0.19965277, 0.21837938, 0.20323476, 0.21672773, 0.21090932,\n", "       0.21973517, 0.20536028, 0.24607524, 0.25846693, 0.21003517,\n", "       0.34409038, 0.42577998, 0.22011306, 0.22539511, 0.2094118 ,\n", "       0.21910094, 0.21993696, 0.20693015, 0.21495249, 0.21464399,\n", "       0.21540069, 0.20319825, 0.2157866 , 0.21697119, 0.21047129,\n", "       0.22061009, 0.2924174 , 0.21394856, 0.22736684, 0.34409038,\n", "       0.42577998, 0.22011306, 0.22539511, 0.2094118 , 0.21910094,\n", "       0.21221645, 0.22495662, 0.20900526, 0.21423491, 0.21207856,\n", "       0.20748307, 0.20601334, 0.20583882, 0.20950089, 0.2924174 ,\n", "       0.21394856, 0.22736684, 0.34409038, 0.42577998])"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["scores"]}, {"cell_type": "code", "execution_count": 50, "id": "e24a0ed3-0aad-4f6c-823a-ca260f5f4349", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(16)"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 52, "id": "15554a27-616e-4584-a0ae-6a72a706eda7", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.4257799827608562)"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 56, "id": "2a15030a-dd55-4e3f-a9a2-44c6008f92ce", "metadata": {}, "outputs": [], "source": ["import langchain_community"]}, {"cell_type": "code", "execution_count": 57, "id": "68db548c-130d-421a-83fd-e33b5a7d9cb5", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "module 'langchain_community' has no attribute 'graphs'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[57]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mlangchain_community\u001b[49m\u001b[43m.\u001b[49m\u001b[43mgraphs\u001b[49m.neo4j_graph(d[\u001b[32m16\u001b[39m][\u001b[33m'\u001b[39m\u001b[33mcontent\u001b[39m\u001b[33m'\u001b[39m])\n", "\u001b[31mAttributeError\u001b[39m: module 'langchain_community' has no attribute 'graphs'"]}], "source": ["langchain_community.graphs.neo4j_graph(d[16]['content'])"]}, {"cell_type": "code", "execution_count": 58, "id": "c4e36511-f9c3-45a8-9399-dbe7108a04f7", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "module 'langchain_community' has no attribute 'graphs'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[58]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mlangchain_community\u001b[49m\u001b[43m.\u001b[49m\u001b[43mgraphs\u001b[49m.neo4j_graph.clean_string_values(d[\u001b[32m16\u001b[39m])\n", "\u001b[31mAttributeError\u001b[39m: module 'langchain_community' has no attribute 'graphs'"]}], "source": ["langchain_community.graphs.neo4j_graph.clean_string_values(d[16])"]}, {"cell_type": "code", "execution_count": null, "id": "f810ed80-7902-4f30-88ed-c6939962dcbd", "metadata": {}, "outputs": [], "source": ["langchain_community.clean"]}, {"cell_type": "code", "execution_count": 1, "id": "d3342ece-de83-4a7a-ae0f-ac13c22ff0d8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.1.2\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["!pip install --upgrade --quiet  langchain langchain-neo4j langchain-openai langchain-experimental neo4j"]}, {"cell_type": "code", "execution_count": 3, "id": "1ec27c1b-3411-404e-8117-07ec1585a26b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: langchain_neo4j in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (0.4.0)\n", "Requirement already satisfied: langchain<0.4.0,>=0.3.7 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain_neo4j) (0.3.23)\n", "Requirement already satisfied: langchain-core<0.4.0,>=0.3.8 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain_neo4j) (0.3.55)\n", "Requirement already satisfied: neo4j<6.0.0,>=5.25.0 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain_neo4j) (5.28.1)\n", "Requirement already satisfied: neo4j-graphrag<2.0.0,>=1.5.0 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain_neo4j) (1.6.1)\n", "Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.8 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain<0.4.0,>=0.3.7->langchain_neo4j) (0.3.8)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.17 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain<0.4.0,>=0.3.7->langchain_neo4j) (0.3.19)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain<0.4.0,>=0.3.7->langchain_neo4j) (2.11.0)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain<0.4.0,>=0.3.7->langchain_neo4j) (2.0.40)\n", "Requirement already satisfied: requests<3,>=2 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain<0.4.0,>=0.3.7->langchain_neo4j) (2.31.0)\n", "Requirement already satisfied: PyYAML>=5.3 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain<0.4.0,>=0.3.7->langchain_neo4j) (6.0.2)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain<0.4.0,>=0.3.7->langchain_neo4j) (4.0.3)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain-core<0.4.0,>=0.3.8->langchain_neo4j) (8.3.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain-core<0.4.0,>=0.3.8->langchain_neo4j) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain-core<0.4.0,>=0.3.8->langchain_neo4j) (24.0)\n", "Requirement already satisfied: typing-extensions>=4.7 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain-core<0.4.0,>=0.3.8->langchain_neo4j) (4.13.0)\n", "Requirement already satisfied: pytz in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from neo4j<6.0.0,>=5.25.0->langchain_neo4j) (2024.1)\n", "Requirement already satisfied: fsspec<2025.0.0,>=2024.9.0 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from neo4j-graphrag<2.0.0,>=1.5.0->langchain_neo4j) (2024.12.0)\n", "Requirement already satisfied: json-repair<0.40.0,>=0.39.1 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from neo4j-graphrag<2.0.0,>=1.5.0->langchain_neo4j) (0.39.1)\n", "Requirement already satisfied: pypdf<6.0.0,>=5.1.0 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from neo4j-graphrag<2.0.0,>=1.5.0->langchain_neo4j) (5.4.0)\n", "Requirement already satisfied: types-pyyaml<7.0.0.0,>=6.0.12.20240917 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from neo4j-graphrag<2.0.0,>=1.5.0->langchain_neo4j) (6.0.12.20250402)\n", "Requirement already satisfied: jsonpointer>=1.9 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.4.0,>=0.3.8->langchain_neo4j) (2.4)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langsmith<0.4,>=0.1.17->langchain<0.4.0,>=0.3.7->langchain_neo4j) (0.27.0)\n", "Requirement already satisfied: <PERSON><PERSON><PERSON><4.0.0,>=3.9.14 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langsmith<0.4,>=0.1.17->langchain<0.4.0,>=0.3.7->langchain_neo4j) (3.10.16)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langsmith<0.4,>=0.1.17->langchain<0.4.0,>=0.3.7->langchain_neo4j) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langsmith<0.4,>=0.1.17->langchain<0.4.0,>=0.3.7->langchain_neo4j) (0.23.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from pydantic<3.0.0,>=2.7.4->langchain<0.4.0,>=0.3.7->langchain_neo4j) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.0 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from pydantic<3.0.0,>=2.7.4->langchain<0.4.0,>=0.3.7->langchain_neo4j) (2.33.0)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from pydantic<3.0.0,>=2.7.4->langchain<0.4.0,>=0.3.7->langchain_neo4j) (0.4.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from requests<3,>=2->langchain<0.4.0,>=0.3.7->langchain_neo4j) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from requests<3,>=2->langchain<0.4.0,>=0.3.7->langchain_neo4j) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from requests<3,>=2->langchain<0.4.0,>=0.3.7->langchain_neo4j) (1.26.18)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from requests<3,>=2->langchain<0.4.0,>=0.3.7->langchain_neo4j) (2024.2.2)\n", "Requirement already satisfied: anyio in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain<0.4.0,>=0.3.7->langchain_neo4j) (4.3.0)\n", "Requirement already satisfied: httpcore==1.* in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain<0.4.0,>=0.3.7->langchain_neo4j) (1.0.5)\n", "Requirement already satisfied: sniffio in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain<0.4.0,>=0.3.7->langchain_neo4j) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain<0.4.0,>=0.3.7->langchain_neo4j) (0.14.0)\n", "Requirement already satisfied: exceptiongroup>=1.0.2 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain<0.4.0,>=0.3.7->langchain_neo4j) (1.2.1)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.1.2\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["!pip3 install langchain_neo4j"]}, {"cell_type": "code", "execution_count": 2, "id": "4a7b8741-cef3-4aa3-8f55-755357760d3f", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "Could not connect to Neo4j database. Please ensure that the url is correct", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mConnectionRefusedError\u001b[39m                    <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/neo4j/_async_compat/network/_bolt_socket.py:409\u001b[39m, in \u001b[36mBoltSocketBase._connect_secure\u001b[39m\u001b[34m(cls, resolved_address, timeout, keep_alive, ssl_context)\u001b[39m\n\u001b[32m    408\u001b[39m log.debug(\u001b[33m\"\u001b[39m\u001b[33m[#0000]  C: <OPEN> \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m\"\u001b[39m, resolved_address)\n\u001b[32m--> \u001b[39m\u001b[32m409\u001b[39m \u001b[43ms\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresolved_address\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    410\u001b[39m s.settimeout(t)\n", "\u001b[31mConnectionRefusedError\u001b[39m: [Errno 61] Connection refused", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mServiceUnavailable\u001b[39m                        <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/neo4j/_sync/io/_bolt_socket.py:328\u001b[39m, in \u001b[36mBoltSocket.connect\u001b[39m\u001b[34m(cls, address, tcp_timeout, deadline, custom_resolver, ssl_context, keep_alive)\u001b[39m\n\u001b[32m    327\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m328\u001b[39m     s = \u001b[38;5;28;43mcls\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_connect_secure\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    329\u001b[39m \u001b[43m        \u001b[49m\u001b[43mresolved_address\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtcp_timeout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkeep_alive\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mssl_context\u001b[49m\n\u001b[32m    330\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    331\u001b[39m     agreed_version, handshake, response = s._handshake(\n\u001b[32m    332\u001b[39m         resolved_address, deadline\n\u001b[32m    333\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/neo4j/_async_compat/network/_bolt_socket.py:426\u001b[39m, in \u001b[36mBoltSocketBase._connect_secure\u001b[39m\u001b[34m(cls, resolved_address, timeout, keep_alive, ssl_context)\u001b[39m\n\u001b[32m    425\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(error, \u001b[38;5;167;01mOSError\u001b[39;00m):\n\u001b[32m--> \u001b[39m\u001b[32m426\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m ServiceUnavailable(\n\u001b[32m    427\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mFailed to establish connection to \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    428\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresolved_address\u001b[38;5;132;01m!r}\u001b[39;00m\u001b[33m (reason \u001b[39m\u001b[38;5;132;01m{\u001b[39;00merror\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m)\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    429\u001b[39m     ) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01merror\u001b[39;00m\n\u001b[32m    430\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m\n", "\u001b[31mServiceUnavailable\u001b[39m: Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [Errno 61] Connection refused)", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mServiceUnavailable\u001b[39m                        <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/langchain_neo4j/graphs/neo4j_graph.py:153\u001b[39m, in \u001b[36mNeo4jGraph.__init__\u001b[39m\u001b[34m(self, url, username, password, database, timeout, sanitize, refresh_schema, driver_config, enhanced_schema)\u001b[39m\n\u001b[32m    152\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m153\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_driver\u001b[49m\u001b[43m.\u001b[49m\u001b[43mverify_connectivity\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    154\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m neo4j.exceptions.ConfigurationError:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/neo4j/_sync/driver.py:1082\u001b[39m, in \u001b[36mDriver.verify_connectivity\u001b[39m\u001b[34m(self, **config)\u001b[39m\n\u001b[32m   1081\u001b[39m session_config = \u001b[38;5;28mself\u001b[39m._read_session_config(config)\n\u001b[32m-> \u001b[39m\u001b[32m1082\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_get_server_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43msession_config\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/neo4j/_sync/driver.py:1297\u001b[39m, in \u001b[36mDriver._get_server_info\u001b[39m\u001b[34m(self, session_config)\u001b[39m\n\u001b[32m   1296\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m._session(session_config) \u001b[38;5;28;01mas\u001b[39;00m session:\n\u001b[32m-> \u001b[39m\u001b[32m1297\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43msession\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_get_server_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/neo4j/_sync/work/session.py:183\u001b[39m, in \u001b[36mSession._get_server_info\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    182\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m._connection\n\u001b[32m--> \u001b[39m\u001b[32m183\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_connect\u001b[49m\u001b[43m(\u001b[49m\u001b[43mREAD_ACCESS\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mliveness_check_timeout\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    184\u001b[39m server_info = \u001b[38;5;28mself\u001b[39m._connection.server_info\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/neo4j/_sync/work/session.py:136\u001b[39m, in \u001b[36mSession._connect\u001b[39m\u001b[34m(self, access_mode, **acquire_kwargs)\u001b[39m\n\u001b[32m    135\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m136\u001b[39m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_connect\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    137\u001b[39m \u001b[43m        \u001b[49m\u001b[43maccess_mode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mauth\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_config\u001b[49m\u001b[43m.\u001b[49m\u001b[43mauth\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43macquire_kwargs\u001b[49m\n\u001b[32m    138\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    139\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m asyncio.CancelledError:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/neo4j/_sync/work/workspace.py:199\u001b[39m, in \u001b[36mWorkspace._connect\u001b[39m\u001b[34m(self, access_mode, auth, **acquire_kwargs)\u001b[39m\n\u001b[32m    198\u001b[39m acquire_kwargs_.update(acquire_kwargs)\n\u001b[32m--> \u001b[39m\u001b[32m199\u001b[39m \u001b[38;5;28mself\u001b[39m._connection = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_pool\u001b[49m\u001b[43m.\u001b[49m\u001b[43macquire\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43macquire_kwargs_\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    200\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m (\n\u001b[32m    201\u001b[39m     target_db.guessed\n\u001b[32m    202\u001b[39m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m._pinned_database\n\u001b[32m   (...)\u001b[39m\u001b[32m    206\u001b[39m     \u001b[38;5;66;03m# support SSR.\u001b[39;00m\n\u001b[32m    207\u001b[39m     \u001b[38;5;66;03m# => we need to fall back to explicit home database resolution\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/neo4j/_sync/io/_pool.py:662\u001b[39m, in \u001b[36mBoltPool.acquire\u001b[39m\u001b[34m(self, access_mode, timeout, database, bookmarks, auth, liveness_check_timeout, database_callback)\u001b[39m\n\u001b[32m    661\u001b[39m deadline = Deadline.from_timeout_or_deadline(timeout)\n\u001b[32m--> \u001b[39m\u001b[32m662\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_acquire\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    663\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43maddress\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mauth\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdeadline\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mliveness_check_timeout\u001b[49m\n\u001b[32m    664\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/neo4j/_sync/io/_pool.py:408\u001b[39m, in \u001b[36mIOPool._acquire\u001b[39m\u001b[34m(self, address, auth, deadline, liveness_check_timeout)\u001b[39m\n\u001b[32m    407\u001b[39m log.debug(\u001b[33m\"\u001b[39m\u001b[33m[#0000]  _: <POOL> trying to hand out new connection\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m408\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mconnection_creator\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/neo4j/_sync/io/_pool.py:230\u001b[39m, in \u001b[36mIOPool._acquire_new_later.<locals>.connection_creator\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m    229\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m230\u001b[39m     connection = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mopener\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    231\u001b[39m \u001b[43m        \u001b[49m\u001b[43maddress\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mauth\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mpool_config\u001b[49m\u001b[43m.\u001b[49m\u001b[43mauth\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdeadline\u001b[49m\n\u001b[32m    232\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    233\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m ServiceUnavailable:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/neo4j/_sync/io/_pool.py:624\u001b[39m, in \u001b[36mBoltPool.open.<locals>.opener\u001b[39m\u001b[34m(addr, auth_manager, deadline)\u001b[39m\n\u001b[32m    623\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mopener\u001b[39m(addr, auth_manager, deadline):\n\u001b[32m--> \u001b[39m\u001b[32m624\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mBolt\u001b[49m\u001b[43m.\u001b[49m\u001b[43mopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    625\u001b[39m \u001b[43m        \u001b[49m\u001b[43maddr\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    626\u001b[39m \u001b[43m        \u001b[49m\u001b[43mauth_manager\u001b[49m\u001b[43m=\u001b[49m\u001b[43mauth_manager\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    627\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdeadline\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdeadline\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    628\u001b[39m \u001b[43m        \u001b[49m\u001b[43mrouting_context\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    629\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpool_config\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpool_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    630\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/neo4j/_sync/io/_bolt.py:369\u001b[39m, in \u001b[36mBolt.open\u001b[39m\u001b[34m(cls, address, auth_manager, deadline, routing_context, pool_config)\u001b[39m\n\u001b[32m    367\u001b[39m     deadline = Deadline(\u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m--> \u001b[39m\u001b[32m369\u001b[39m s, protocol_version, handshake, data = \u001b[43mBoltSocket\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    370\u001b[39m \u001b[43m    \u001b[49m\u001b[43maddress\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    371\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtcp_timeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpool_config\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnection_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    372\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdeadline\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdeadline\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    373\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcustom_resolver\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpool_config\u001b[49m\u001b[43m.\u001b[49m\u001b[43mresolver\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    374\u001b[39m \u001b[43m    \u001b[49m\u001b[43mssl_context\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpool_config\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget_ssl_context\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    375\u001b[39m \u001b[43m    \u001b[49m\u001b[43mkeep_alive\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpool_config\u001b[49m\u001b[43m.\u001b[49m\u001b[43mkeep_alive\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    376\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    378\u001b[39m pool_config.protocol_version = protocol_version\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/neo4j/_sync/io/_bolt_socket.py:376\u001b[39m, in \u001b[36mBoltSocket.connect\u001b[39m\u001b[34m(cls, address, tcp_timeout, deadline, custom_resolver, ssl_context, keep_alive)\u001b[39m\n\u001b[32m    375\u001b[39m error_strs = \u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m\"\u001b[39m.join(\u001b[38;5;28mmap\u001b[39m(\u001b[38;5;28mstr\u001b[39m, errors))\n\u001b[32m--> \u001b[39m\u001b[32m376\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m ServiceUnavailable(\n\u001b[32m    377\u001b[39m     \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mCouldn\u001b[39m\u001b[33m'\u001b[39m\u001b[33mt connect to \u001b[39m\u001b[38;5;132;01m{\u001b[39;00maddress\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m (resolved to \u001b[39m\u001b[38;5;132;01m{\u001b[39;00maddress_strs\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m):\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    378\u001b[39m     \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00merror_strs\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    379\u001b[39m ) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01merrors\u001b[39;00m[\u001b[32m0\u001b[39m]\n", "\u001b[31mServiceUnavailable\u001b[39m: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):\nFailed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [Errno 61] Connection refused)\nFailed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [Errno 61] Connection refused)", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 9\u001b[39m\n\u001b[32m      6\u001b[39m os.environ[\u001b[33m\"\u001b[39m\u001b[33mNEO4J_USERNAME\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[33m\"\u001b[39m\u001b[33mneo4j\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m      7\u001b[39m os.environ[\u001b[33m\"\u001b[39m\u001b[33mNEO4J_PASSWORD\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[33m\"\u001b[39m\u001b[33mpassword\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m9\u001b[39m graph = \u001b[43mNeo4jGraph\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrefresh_schema\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/langchain_neo4j/graphs/neo4j_graph.py:160\u001b[39m, in \u001b[36mNeo4jGraph.__init__\u001b[39m\u001b[34m(self, url, username, password, database, timeout, sanitize, refresh_schema, driver_config, enhanced_schema)\u001b[39m\n\u001b[32m    155\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    156\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mCould not connect to Neo4j database. \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    157\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mPlease ensure that the driver config is correct\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    158\u001b[39m     )\n\u001b[32m    159\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m neo4j.exceptions.ServiceUnavailable:\n\u001b[32m--> \u001b[39m\u001b[32m160\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>r\u001b[39;00m(\n\u001b[32m    161\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mCould not connect to Neo4j database. \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    162\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mPlease ensure that the url is correct\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    163\u001b[39m     )\n\u001b[32m    164\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m neo4j.exceptions.AuthError:\n\u001b[32m    165\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    166\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mCould not connect to Neo4j database. \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    167\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mPlease ensure that the username and password are correct\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    168\u001b[39m     )\n", "\u001b[31mValueError\u001b[39m: Could not connect to Neo4j database. Please ensure that the url is correct"]}], "source": ["import os\n", "\n", "from langchain_neo4j import Neo4jGraph\n", "\n", "os.environ[\"NEO4J_URI\"] = \"bolt://localhost:7687\"\n", "os.environ[\"NEO4J_USERNAME\"] = \"neo4j\"\n", "os.environ[\"NEO4J_PASSWORD\"] = \"password\"\n", "\n", "graph = Neo4jGraph(refresh_schema=False)"]}, {"cell_type": "code", "execution_count": null, "id": "c5723c73-f6af-4563-b05d-3aae9ae10e7f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "lang_stuff", "language": "python", "name": "lang_stuff"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}