Collecting langchain-core
  Using cached langchain_core-0.3.49-py3-none-any.whl.metadata (5.9 kB)
Collecting langgraph
  Downloading langgraph-0.3.21-py3-none-any.whl.metadata (7.7 kB)
Collecting langsmith<0.4,>=0.1.125 (from langchain-core)
  Using cached langsmith-0.3.19-py3-none-any.whl.metadata (15 kB)
Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain-core) (8.3.0)
Collecting jsonpatch<2.0,>=1.33 (from langchain-core)
  Using cached jsonpatch-1.33-py2.py3-none-any.whl.metadata (3.0 kB)
Requirement already satisfied: PyYAML>=5.3 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain-core) (6.0.1)
Requirement already satisfied: packaging<25,>=23.2 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain-core) (24.0)
Requirement already satisfied: typing-extensions>=4.7 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langchain-core) (4.11.0)
Collecting pydantic<3.0.0,>=2.5.2 (from langchain-core)
  Using cached pydantic-2.11.0-py3-none-any.whl.metadata (63 kB)
Collecting langgraph-checkpoint<3.0.0,>=2.0.10 (from langgraph)
  Downloading langgraph_checkpoint-2.0.23-py3-none-any.whl.metadata (4.6 kB)
Collecting langgraph-prebuilt<0.2,>=0.1.1 (from langgraph)
  Downloading langgraph_prebuilt-0.1.7-py3-none-any.whl.metadata (5.0 kB)
Collecting langgraph-sdk<0.2.0,>=0.1.42 (from langgraph)
  Downloading langgraph_sdk-0.1.60-py3-none-any.whl.metadata (1.8 kB)
Collecting xxhash<4.0.0,>=3.5.0 (from langgraph)
  Downloading xxhash-3.5.0-cp39-cp39-macosx_11_0_arm64.whl.metadata (12 kB)
Requirement already satisfied: jsonpointer>=1.9 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from jsonpatch<2.0,>=1.33->langchain-core) (2.4)
Collecting ormsgpack<2.0.0,>=1.8.0 (from langgraph-checkpoint<3.0.0,>=2.0.10->langgraph)
  Downloading ormsgpack-1.9.0-cp39-cp39-macosx_10_12_x86_64.macosx_11_0_arm64.macosx_10_12_universal2.whl.metadata (43 kB)
[?25l     [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m0.0/43.5 kB[0m [31m?[0m eta [36m-:--:--[0m
[2K     [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m43.5/43.5 kB[0m [31m1.4 MB/s[0m eta [36m0:00:00[0m
[?25hRequirement already satisfied: httpx>=0.25.2 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langgraph-sdk<0.2.0,>=0.1.42->langgraph) (0.27.0)
Collecting orjson>=3.10.1 (from langgraph-sdk<0.2.0,>=0.1.42->langgraph)
  Downloading orjson-3.10.16-cp39-cp39-macosx_10_15_x86_64.macosx_11_0_arm64.macosx_10_15_universal2.whl.metadata (41 kB)
[?25l     [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m0.0/41.8 kB[0m [31m?[0m eta [36m-:--:--[0m
[2K     [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m41.8/41.8 kB[0m [31m3.2 MB/s[0m eta [36m0:00:00[0m
[?25hRequirement already satisfied: requests<3,>=2 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langsmith<0.4,>=0.1.125->langchain-core) (2.31.0)
Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from langsmith<0.4,>=0.1.125->langchain-core) (1.0.0)
Collecting zstandard<0.24.0,>=0.23.0 (from langsmith<0.4,>=0.1.125->langchain-core)
  Downloading zstandard-0.23.0-cp39-cp39-macosx_11_0_arm64.whl.metadata (3.0 kB)
Collecting annotated-types>=0.6.0 (from pydantic<3.0.0,>=2.5.2->langchain-core)
  Using cached annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)
Collecting pydantic-core==2.33.0 (from pydantic<3.0.0,>=2.5.2->langchain-core)
  Downloading pydantic_core-2.33.0-cp39-cp39-macosx_11_0_arm64.whl.metadata (6.8 kB)
Collecting typing-extensions>=4.7 (from langchain-core)
  Using cached typing_extensions-4.13.0-py3-none-any.whl.metadata (3.0 kB)
Collecting typing-inspection>=0.4.0 (from pydantic<3.0.0,>=2.5.2->langchain-core)
  Using cached typing_inspection-0.4.0-py3-none-any.whl.metadata (2.6 kB)
Requirement already satisfied: anyio in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph) (4.3.0)
Requirement already satisfied: certifi in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph) (2024.2.2)
Requirement already satisfied: httpcore==1.* in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph) (1.0.5)
Requirement already satisfied: idna in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph) (3.7)
Requirement already satisfied: sniffio in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph) (1.3.1)
Requirement already satisfied: h11<0.15,>=0.13 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from httpcore==1.*->httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph) (0.14.0)
Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from requests<3,>=2->langsmith<0.4,>=0.1.125->langchain-core) (3.3.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from requests<3,>=2->langsmith<0.4,>=0.1.125->langchain-core) (1.26.18)
Requirement already satisfied: exceptiongroup>=1.0.2 in /Users/<USER>/Desktop/codes/venvs/fruit_tracker/lib/python3.9/site-packages (from anyio->httpx>=0.25.2->langgraph-sdk<0.2.0,>=0.1.42->langgraph) (1.2.1)
Using cached langchain_core-0.3.49-py3-none-any.whl (420 kB)
Downloading langgraph-0.3.21-py3-none-any.whl (138 kB)
[?25l   [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m0.0/138.0 kB[0m [31m?[0m eta [36m-:--:--[0m
[2K   [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m138.0/138.0 kB[0m [31m4.0 MB/s[0m eta [36m0:00:00[0m
[?25hUsing cached jsonpatch-1.33-py2.py3-none-any.whl (12 kB)
Downloading langgraph_checkpoint-2.0.23-py3-none-any.whl (41 kB)
[?25l   [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m0.0/41.9 kB[0m [31m?[0m eta [36m-:--:--[0m
[2K   [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m41.9/41.9 kB[0m [31m7.7 MB/s[0m eta [36m0:00:00[0m
[?25hDownloading langgraph_prebuilt-0.1.7-py3-none-any.whl (25 kB)
Downloading langgraph_sdk-0.1.60-py3-none-any.whl (47 kB)
[?25l   [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m0.0/47.1 kB[0m [31m?[0m eta [36m-:--:--[0m
[2K   [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m47.1/47.1 kB[0m [31m7.5 MB/s[0m eta [36m0:00:00[0m
[?25hUsing cached langsmith-0.3.19-py3-none-any.whl (351 kB)
Using cached pydantic-2.11.0-py3-none-any.whl (442 kB)
Downloading pydantic_core-2.33.0-cp39-cp39-macosx_11_0_arm64.whl (1.9 MB)
[?25l   [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m0.0/1.9 MB[0m [31m?[0m eta [36m-:--:--[0m
[2K   [91m━━━━━━━━━━[0m[91m╸[0m[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m0.5/1.9 MB[0m [31m13.9 MB/s[0m eta [36m0:00:01[0m
[2K   [91m━━━━━━━━━━━━━━━━━━━━━━[0m[90m╺[0m[90m━━━━━━━━━━━━━━━━━[0m [32m1.0/1.9 MB[0m [31m14.4 MB/s[0m eta [36m0:00:01[0m
[2K   [91m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m[91m╸[0m [32m1.9/1.9 MB[0m [31m18.2 MB/s[0m eta [36m0:00:01[0m
[2K   [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m1.9/1.9 MB[0m [31m16.5 MB/s[0m eta [36m0:00:00[0m
[?25hUsing cached typing_extensions-4.13.0-py3-none-any.whl (45 kB)
Downloading xxhash-3.5.0-cp39-cp39-macosx_11_0_arm64.whl (30 kB)
Using cached annotated_types-0.7.0-py3-none-any.whl (13 kB)
Downloading orjson-3.10.16-cp39-cp39-macosx_10_15_x86_64.macosx_11_0_arm64.macosx_10_15_universal2.whl (249 kB)
[?25l   [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m0.0/249.5 kB[0m [31m?[0m eta [36m-:--:--[0m
[2K   [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m249.5/249.5 kB[0m [31m21.5 MB/s[0m eta [36m0:00:00[0m
[?25hDownloading ormsgpack-1.9.0-cp39-cp39-macosx_10_12_x86_64.macosx_11_0_arm64.macosx_10_12_universal2.whl (382 kB)
[?25l   [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m0.0/382.9 kB[0m [31m?[0m eta [36m-:--:--[0m
[2K   [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m382.9/382.9 kB[0m [31m29.2 MB/s[0m eta [36m0:00:00[0m
[?25hUsing cached typing_inspection-0.4.0-py3-none-any.whl (14 kB)
Downloading zstandard-0.23.0-cp39-cp39-macosx_11_0_arm64.whl (633 kB)
[?25l   [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m0.0/633.7 kB[0m [31m?[0m eta [36m-:--:--[0m
[2K   [90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[0m [32m633.7/633.7 kB[0m [31m38.0 MB/s[0m eta [36m0:00:00[0m
[?25hInstalling collected packages: zstandard, xxhash, typing-extensions, ormsgpack, orjson, jsonpatch, annotated-types, typing-inspection, pydantic-core, pydantic, langsmith, langgraph-sdk, langchain-core, langgraph-checkpoint, langgraph-prebuilt, langgraph
  Attempting uninstall: typing-extensions
    Found existing installation: typing_extensions 4.11.0
    Uninstalling typing_extensions-4.11.0:
      Successfully uninstalled typing_extensions-4.11.0
Successfully installed annotated-types-0.7.0 jsonpatch-1.33 langchain-core-0.3.49 langgraph-0.3.21 langgraph-checkpoint-2.0.23 langgraph-prebuilt-0.1.7 langgraph-sdk-0.1.60 langsmith-0.3.19 orjson-3.10.16 ormsgpack-1.9.0 pydantic-2.11.0 pydantic-core-2.33.0 typing-extensions-4.13.0 typing-inspection-0.4.0 xxhash-3.5.0 zstandard-0.23.0
