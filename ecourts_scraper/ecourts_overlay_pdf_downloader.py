"""
eCourts Overlay PDF Downloader
Handles PDFs that open in an overlay/modal viewer on the same page
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
import os
import logging
from datetime import datetime, timedelta
import re
from typing import Optional, Dict, List
from contextlib import contextmanager
import json

# Import captcha solvers from separate module
from captcha_solvers import CaptchaSolver, TwoCaptchaSolver, AntiCaptchaSolver

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TimingStats:
    """Track timing statistics for various operations"""
    
    def __init__(self):
        self.stats = {
            'overall_start': None,
            'overall_end': None,
            'search_times': [],
            'download_times': [],
            'captcha_times': [],
            'page_navigation_times': [],
            'modal_operation_times': [],
            'case_processing_times': [],
            'restart_times': []
        }
        
    def start_overall(self):
        """Mark the start of overall execution"""
        self.stats['overall_start'] = time.time()
        
    def end_overall(self):
        """Mark the end of overall execution"""
        self.stats['overall_end'] = time.time()
        
    def add_timing(self, category: str, duration: float):
        """Add a timing measurement to a category"""
        if category in self.stats and isinstance(self.stats[category], list):
            self.stats[category].append(duration)
            
    def get_summary(self) -> Dict:
        """Get a summary of all timing statistics"""
        summary = {}
        
        # Overall execution time
        if self.stats['overall_start'] and self.stats['overall_end']:
            overall_time = self.stats['overall_end'] - self.stats['overall_start']
            summary['total_execution_time'] = str(timedelta(seconds=int(overall_time)))
            summary['total_execution_seconds'] = round(overall_time, 2)
        
        # Calculate averages and totals for each category
        for category, times in self.stats.items():
            if isinstance(times, list) and times:
                summary[f'{category}_count'] = len(times)
                summary[f'{category}_total'] = round(sum(times), 2)
                summary[f'{category}_average'] = round(sum(times) / len(times), 2)
                summary[f'{category}_min'] = round(min(times), 2)
                summary[f'{category}_max'] = round(max(times), 2)
                
        return summary
    
    def save_to_file(self, filepath: str):
        """Save timing statistics to a JSON file"""
        summary = self.get_summary()
        summary['timestamp'] = datetime.now().isoformat()
        
        with open(filepath, 'w') as f:
            json.dump(summary, f, indent=2)
            
    def print_summary(self):
        """Print a formatted summary of timing statistics"""
        summary = self.get_summary()
        
        print("\n" + "="*60)
        print("TIMING STATISTICS SUMMARY")
        print("="*60)
        
        if 'total_execution_time' in summary:
            print(f"\nTotal Execution Time: {summary['total_execution_time']} ({summary['total_execution_seconds']} seconds)")
        
        categories = [
            ('search_times', 'Search Operations'),
            ('download_times', 'PDF Downloads'),
            ('captcha_times', 'Captcha Solving'),
            ('page_navigation_times', 'Page Navigation'),
            ('modal_operation_times', 'Modal Operations'),
            ('case_processing_times', 'Case Processing'),
            ('restart_times', 'Search Restarts')
        ]
        
        for key, label in categories:
            if f'{key}_count' in summary and summary[f'{key}_count'] > 0:
                print(f"\n{label}:")
                print(f"  Count: {summary[f'{key}_count']}")
                print(f"  Total Time: {summary[f'{key}_total']} seconds")
                print(f"  Average: {summary[f'{key}_average']} seconds")
                print(f"  Min: {summary[f'{key}_min']} seconds")
                print(f"  Max: {summary[f'{key}_max']} seconds")
        
        print("\n" + "="*60)


@contextmanager
def time_operation(timing_stats: TimingStats, category: str, operation_name: str = None):
    """Context manager to time an operation"""
    start_time = time.time()
    if operation_name:
        logger.info(f"Starting {operation_name}...")
    
    try:
        yield
    finally:
        duration = time.time() - start_time
        timing_stats.add_timing(category, duration)
        if operation_name:
            logger.info(f"Completed {operation_name} in {duration:.2f} seconds")


class ECourtsOverlayPDFDownloader:
    """Downloads PDFs from overlay/modal viewer"""
    
    def __init__(self, output_dir: str = "overlay_pdf_downloads", headless: bool = False, 
                 captcha_solver: Optional[CaptchaSolver] = None):
        self.base_url = "https://judgments.ecourts.gov.in/pdfsearch/"
        self.search_url = f"{self.base_url}index.php"
        self.output_dir = output_dir
        self.headless = headless
        self.driver = None
        self.captcha_solver = captcha_solver
        self.timing_stats = TimingStats()
        
        # Create output directories
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "pdfs"), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "screenshots"), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "logs"), exist_ok=True)
        
    def setup_driver(self):
        """Initialize Selenium WebDriver"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument("--headless")
        
        # Basic options
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        
        # PDF download preferences
        download_dir = os.path.abspath(os.path.join(self.output_dir, "pdfs"))
        prefs = {
            "download.default_directory": download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "plugins.always_open_pdf_externally": False,  # Keep PDF viewer
            "profile.default_content_setting_values.automatic_downloads": 1,
            "profile.default_content_settings.popups": 0,
            "safebrowsing.enabled": False,
            "safebrowsing.disable_download_protection": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.driver.implicitly_wait(10)
        
        # Enable Chrome DevTools Protocol
        self.driver.execute_cdp_cmd('Page.setDownloadBehavior', {
            'behavior': 'allow',
            'downloadPath': download_dir
        })
        
        logger.info(f"WebDriver initialized with download directory: {download_dir}")
        
    def close_driver(self):
        """Close the WebDriver"""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver closed")
    
    def perform_search(self, keyword: str = "GST", court_type: str = "High Court", 
                      search_type: str = "phrase") -> bool:
        """Perform a search to get results"""
        with time_operation(self.timing_stats, 'search_times', f'search for "{keyword}"'):
            try:
                # Navigate to search page
                self.driver.get(self.search_url)
                time.sleep(2)
                
                # Select court
                court_dropdown = self.driver.find_element(By.ID, "fcourt_type")
                Select(court_dropdown).select_by_visible_text(court_type)
                
                # Set search type
                search_type_map = {
                    'phrase': 'inlineRadio1',
                    'any_words': 'inlineRadio2',
                    'all_words': 'inlineRadio3'
                }
                radio_id = search_type_map.get(search_type, 'inlineRadio1')
                self.driver.find_element(By.ID, radio_id).click()
                
                # Enter keyword
                keyword_input = self.driver.find_element(By.ID, "search_text")
                keyword_input.clear()
                keyword_input.send_keys(keyword)
                
                # Handle captcha
                with time_operation(self.timing_stats, 'captcha_times', 'captcha solving'):
                    captcha_element = self.driver.find_element(By.ID, "captcha_image")
                    captcha_screenshot = captcha_element.screenshot_as_png
                    
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    captcha_path = os.path.join(self.output_dir, f"captcha_{timestamp}.png")
                    
                    with open(captcha_path, 'wb') as f:
                        f.write(captcha_screenshot)
                    
                    logger.info(f"Captcha saved to: {captcha_path}")
                    
                    # Solve captcha
                    captcha_text = None
                    if self.captcha_solver:
                        logger.info("Using automatic captcha solver...")
                        captcha_text = self.captcha_solver.solve_image_captcha(captcha_path)
                        
                    if not captcha_text:
                        # Fall back to manual input
                        print(f"\nCaptcha saved to: {captcha_path}")
                        captcha_text = input("Please enter the captcha text: ")
                    
                    captcha_input = self.driver.find_element(By.ID, "captcha")
                    captcha_input.clear()
                    captcha_input.send_keys(captcha_text)
                
                # Submit search
                search_button = self.driver.find_element(By.ID, "main_search")
                search_button.click()
                
                # Wait for results
                logger.info("Waiting for results to load...")
                time.sleep(20)
                
                return True
                
            except Exception as e:
                logger.error(f"Error performing search: {e}")
                return False
    
    def download_pdf_from_overlay(self, case_number: str, row_index: int) -> dict:
        """Click case to open PDF in overlay and download it"""
        with time_operation(self.timing_stats, 'download_times', f'downloading {case_number}'):
            result = {
                'success': False,
                'case_number': case_number,
                'error': None,
                'method': None,
                'download_time': 0
            }
            
            download_start = time.time()
            
            try:
                # Find all case buttons
                case_buttons = self.driver.find_elements(By.CSS_SELECTOR, "#report_body tr[role='row'] button.btn-link")
                
                if row_index >= len(case_buttons):
                    result['error'] = f"Row index {row_index} out of range"
                    return result
                
                # Get the button for this case
                case_button = case_buttons[row_index]
                
                # Scroll to button and click
                with time_operation(self.timing_stats, 'modal_operation_times', 'opening modal'):
                    click_sucess = False
                    logger.info(f"Clicking case button for {case_number}...")
                    count = 0
                    while (not click_sucess) and (count < 5):
                        try:
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", case_button)
                            time.sleep(5)
                            case_button.click()
                            click_sucess = True
                        except Exception as e:
                            count+=1
                            screenshot_path = os.path.join(self.output_dir, "screenshots", f"error_{case_number.replace('/', '_')}.png")
                            self.driver.save_screenshot(screenshot_path)

                    # Wait for modal to appear
                    logger.info("Waiting for modal to appear...")
                    try:
                        modal = WebDriverWait(self.driver, 10).until(
                            EC.presence_of_element_located((By.ID, "viewFiles"))
                        )
                        logger.info("Modal found!")
                    except:
                        result['error'] = "Modal did not appear"
                        return result
                
                # Wait a bit more for PDF to load
                time.sleep(5)
                
                # Take screenshot of the overlay
                screenshot_path = os.path.join(self.output_dir, "screenshots", f"overlay_{case_number.replace('/', '_')}.png")
                self.driver.save_screenshot(screenshot_path)
                logger.info(f"Screenshot saved to: {screenshot_path}")
                
                # Strategy 1: Try to access the nested iframe structure
                logger.info("Attempting to access nested iframe structure...")
                try:
                    # Store the main window handle
                    main_window = self.driver.current_window_handle
                    
                    # First, find the object element within the modal
                    try:
                        #viewFiles-body
                        object_element = modal.find_element(By.CSS_SELECTOR, "#viewFiles-body object")
                        logger.info("Found object element")
                        
                        # Get the data URL from the object element
                        data_url = object_element.get_attribute("data")
                        logger.info(f"Object data URL: {data_url}")
                        
                        # Strategy 1A: Try to download the PDF directly from the temporary URL
                        if data_url and data_url.endswith('.pdf'):
                            logger.info("Attempting to download PDF from temporary URL...")
                            try:
                                # Extract filename from URL or use case number
                                filename = f"{case_number.replace('/', '_')}.pdf"
                                
                                # Use JavaScript to trigger download
                                download_script = f"""
                                var link = document.createElement('a');
                                link.href = '{data_url}';
                                link.download = '{filename}';
                                link.style.display = 'none';
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                                """
                                self.driver.execute_script(download_script)
                                logger.info("Triggered download via JavaScript")
                                time.sleep(1)  # Wait for download to complete
                                
                                # Check if file was downloaded
                                pdf_dir = os.path.join(self.output_dir, "pdfs")
                                files = os.listdir(pdf_dir) if os.path.exists(pdf_dir) else []
                                if any(f.endswith('.pdf') for f in files):
                                    result['success'] = True
                                    result['method'] = "direct_url_download"
                                    result['download_time'] = time.time() - download_start
                                    self.driver.switch_to.default_content()
                                    return result
                            except Exception as e:
                                logger.error(f"Error downloading from URL: {e}")
                        
                        # The iframe might be inside the object element's shadow DOM or loaded dynamically
                        # Let's try to find iframes within the object element
                        # First, let's check all iframes in the entire document
                        
                    except Exception as e:
                        logger.info(f"Object element not found or not accessible: {e}")
                    
                    # Make sure we're back in default content
                    self.driver.switch_to.default_content()                                
                    
                except Exception as e:
                    logger.error(f"Error donwloading file structure: {e}")
                    # Make sure we're back in the main content
                    self.driver.switch_to.default_content()
                            
                # Or try to find close button
                close_selectors = [
                    ".close", "button.close", "[aria-label='Close']",
                    "button[title*='close' i]", "[class*='close' i]"
                ]
                
                for selector in close_selectors:
                    try:
                        close_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if close_btn.is_displayed():
                            close_btn.click()
                            break
                    except:
                        continue
                
                result['error'] = "Could not find or click download button in overlay"
                result['download_time'] = time.time() - download_start
                
            except Exception as e:
                result['error'] = str(e)
                result['download_time'] = time.time() - download_start
                logger.error(f"Error: {e}")
            finally:
                # Always try to close the modal before returning
                with time_operation(self.timing_stats, 'modal_operation_times', 'closing modal'):
                    try:
                        logger.info("Attempting to close modal...")
                        # Try to find and click close button
                        close_selectors = [
                            "#viewFiles .btn-close",
                            "#viewFiles button.close",
                            "#viewFiles [aria-label='Close']",
                            ".modal-header .btn-close",
                            ".modal-header button.close",
                            "button[data-bs-dismiss='modal']"
                        ]
                        
                        for selector in close_selectors:
                            try:
                                close_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                                if close_btn.is_displayed():
                                    close_btn.click()
                                    logger.info(f"Closed modal using selector: {selector}")
                                    time.sleep(1)  # Wait for modal to close
                                    break
                            except:
                                continue
                        
                        # If close button didn't work, try ESC key
                        if self.driver.find_elements(By.ID, "viewFiles"):
                            try:
                                body = self.driver.find_element(By.TAG_NAME, "body")
                                body.send_keys(Keys.ESCAPE)
                                logger.info("Sent ESC key to close modal")
                                time.sleep(1)
                            except:
                                pass
                                
                    except Exception as e:
                        logger.debug(f"Error closing modal: {e}")
            
            return result
    
    def _save_resume_point(self, case_number: int):
        """Save the resume point to config.py"""
        try:
            config_path = "config.py"
            
            # Read existing config
            config_content = ""
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config_content = f.read()
            
            # Check if RESUME_FROM_CASE already exists
            import re
            resume_pattern = r'^RESUME_FROM_CASE\s*=\s*\d+'
            
            if re.search(resume_pattern, config_content, re.MULTILINE):
                # Update existing value
                config_content = re.sub(
                    resume_pattern,
                    f'RESUME_FROM_CASE = {case_number}',
                    config_content,
                    flags=re.MULTILINE
                )
            else:
                # Add new value
                if config_content and not config_content.endswith('\n'):
                    config_content += '\n'
                config_content += f'\n# Resume point for failed downloads\n'
                config_content += f'RESUME_FROM_CASE = {case_number}\n'
            
            # Write back to config
            with open(config_path, 'w') as f:
                f.write(config_content)
            
            logger.info(f"Saved resume point to config.py: RESUME_FROM_CASE = {case_number}")
            print(f"\n⚠️  Resume point saved: You can resume from case {case_number} in the next run")
            
        except Exception as e:
            logger.error(f"Failed to save resume point: {e}")
            print(f"\n⚠️  Could not save resume point to config.py. Please note: Resume from case {case_number}")
    
    def download_pdfs_from_search(self, max_cases: int = 2):
        """Perform search and download PDFs from overlay viewer with pagination"""
        # Start overall timing
        self.timing_stats.start_overall()
        
        if not self.driver:
            self.setup_driver()
        
        # Store search parameters for restart
        search_params = {
            'keyword': 'GST',
            'court_type': 'High Court',
            'search_type': 'phrase'
        }
        
        # Perform initial search
        if not self.perform_search(**search_params):
            return {"error": "Search failed"}
        
        # Change results per page to 1000
        try:
            time.sleep(10)  # Wait for search results to load
            logger.info("Changing results per page to 1000...")
            # Find the dropdown for results per page
            length_dropdown = self.driver.find_element(By.CSS_SELECTOR, "#example_pdf_length select")
            select = Select(length_dropdown)
            
            # Select 1000 option
            select.select_by_value("1000")
            logger.info("Selected 1000 results per page")
            
            # Wait for the page to reload with more results
            time.sleep(5)
        except Exception as e:
            logger.warning(f"Could not change results per page: {e}")
            # Continue with default 10 results per page
        
        results = []
        total_processed = 0
        current_page = 1
        consecutive_failures = 0
        max_consecutive_failures = 15
        downloads_since_restart = 0
        restart_after_downloads = 25
        
        # Get initial file count
        pdf_dir = os.path.join(self.output_dir, "pdfs")
        initial_files = set(os.listdir(pdf_dir)) if os.path.exists(pdf_dir) else set()
        
        # Check if we should resume from a previous run
        resume_from = 0
        try:
            import config
            if hasattr(config, 'RESUME_FROM_CASE'):
                resume_from = config.RESUME_FROM_CASE
                logger.info(f"Resuming from case number: {resume_from}")
        except:
            pass
        
        while total_processed < max_cases:
            logger.info(f"\nProcessing page {current_page}...")
            
            # Get case information for current page
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Look for the results table
            tbody = soup.find('tbody', id='report_body')
            
            if not tbody:
                if current_page == 1:
                    # Save page source for debugging
                    debug_path = os.path.join(self.output_dir, "debug_page.html")
                    with open(debug_path, 'w', encoding='utf-8') as f:
                        f.write(self.driver.page_source)
                    logger.error(f"No results table found. Page saved to {debug_path}")
                    
                    # Take a screenshot
                    screenshot_path = os.path.join(self.output_dir, "no_results_screenshot.png")
                    self.driver.save_screenshot(screenshot_path)
                    logger.error(f"Screenshot saved to {screenshot_path}")
                    
                    return {"error": "No results found - check debug files"}
                else:
                    logger.info("No more results on this page")
                    break
            
            rows = tbody.find_all('tr', role='row')
            logger.info(f"Found {len(rows)} result rows on page {current_page}")
            
            # Process cases on current page
            cases_on_page = 0
            for i, row in enumerate(rows):
                if total_processed >= max_cases:
                    break
                
                # Time each case processing
                case_start_time = time.time()
                
                # Extract case number
                case_button = row.find('button', class_='btn-link')
                if not case_button:
                    continue
                
                case_text = case_button.get_text(strip=True)
                case_number = case_text.split(' of ')[0].strip() if ' of ' in case_text else f"CASE_{total_processed}"
                
                logger.info(f"\n{'='*50}")
                logger.info(f"Processing case {total_processed + 1}/{max_cases}: {case_number}")
                logger.info(f"{'='*50}")
                
                # Skip if we're resuming and haven't reached the resume point
                if total_processed < resume_from:
                    total_processed += 1
                    logger.info(f"Skipping case {total_processed} (resuming from {resume_from})")
                    continue
                
                # Try to download PDF
                download_result = self.download_pdf_from_overlay(case_number, i)
                
                # Add case processing time
                case_duration = time.time() - case_start_time
                self.timing_stats.add_timing('case_processing_times', case_duration)
                logger.info(f"Case processing completed in {case_duration:.2f} seconds")
                
                results.append(download_result)
                total_processed += 1
                cases_on_page += 1
                
                # Track consecutive failures and successful downloads
                if download_result['success']:
                    consecutive_failures = 0
                    downloads_since_restart += 1
                    logger.info(f"Downloads since last restart: {downloads_since_restart}/{restart_after_downloads}")
                else:
                    consecutive_failures += 1
                    logger.warning(f"Consecutive failures: {consecutive_failures}/{max_consecutive_failures}")
                    
                    # Check if we've hit the failure limit
                    if consecutive_failures >= max_consecutive_failures:
                        logger.error(f"Reached {max_consecutive_failures} consecutive failures. Stopping process.")
                        
                        # Save resume point to config.py
                        self._save_resume_point(total_processed - max_consecutive_failures)
                        
                        # End overall timing
                        self.timing_stats.end_overall()
                        
                        # Save timing stats
                        timing_file = os.path.join(self.output_dir, "logs", f"timing_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
                        self.timing_stats.save_to_file(timing_file)
                        
                        return {
                            'total_cases': len(results),
                            'results': results,
                            'new_files': list(set(os.listdir(pdf_dir)) - initial_files) if os.path.exists(pdf_dir) else [],
                            'pages_processed': current_page,
                            'stopped_early': True,
                            'resume_from': total_processed,
                            'reason': f'Reached {max_consecutive_failures} consecutive failures',
                            'timestamp': datetime.now().isoformat(),
                            'timing_stats': self.timing_stats.get_summary()
                        }
                
                # Check if we need to restart fresh
                if downloads_since_restart >= restart_after_downloads and total_processed < max_cases:
                    with time_operation(self.timing_stats, 'restart_times', 'restarting search'):
                        logger.info(f"\nReached {restart_after_downloads} downloads. Starting fresh...")
                        
                        # Close current tab and start fresh
                        self.driver.get(self.search_url)  # Navigate away first
                        time.sleep(2)
                        
                        # Perform a new search
                        logger.info("Performing new search...")
                        if not self.perform_search(**search_params):
                            logger.error("Failed to perform new search")
                            
                            # End overall timing
                            self.timing_stats.end_overall()
                            
                            return {
                                'total_cases': len(results),
                                'results': results,
                                'new_files': list(set(os.listdir(pdf_dir)) - initial_files) if os.path.exists(pdf_dir) else [],
                                'pages_processed': current_page,
                                'stopped_early': True,
                                'resume_from': total_processed,
                                'reason': 'Failed to restart search',
                                'timestamp': datetime.now().isoformat(),
                                'timing_stats': self.timing_stats.get_summary()
                            }
                        
                        # Change results per page to 1000 again
                        try:
                            time.sleep(10)
                            logger.info("Changing results per page to 1000...")
                            length_dropdown = self.driver.find_element(By.CSS_SELECTOR, "#example_pdf_length select")
                            select = Select(length_dropdown)
                            select.select_by_value("1000")
                            logger.info("Selected 1000 results per page")
                            time.sleep(5)
                        except Exception as e:
                            logger.warning(f"Could not change results per page: {e}")
                        
                        # Navigate to the appropriate page if we were on a different page
                        if current_page > 1:
                            logger.info(f"Navigating to page {current_page}...")
                            for page_num in range(2, current_page + 1):
                                try:
                                    # Find and click next button
                                    next_selectors = [
                                        ".paginate_button.next",
                                        "#report_body_next",
                                        "a[aria-label='Next']",
                                        "button[aria-label='Next']"
                                    ]
                                    
                                    for selector in next_selectors:
                                        try:
                                            next_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                                            if next_btn.is_displayed() and next_btn.is_enabled():
                                                next_btn.click()
                                                time.sleep(5)
                                                break
                                        except:
                                            continue
                                except Exception as e:
                                    logger.warning(f"Could not navigate to page {page_num}: {e}")
                                    break
                        
                        # Reset counter
                        downloads_since_restart = 0
                        logger.info("Fresh start completed. Continuing downloads...\n")
                        
                        # Need to break out of the current page loop to re-read the page content
                        break
                    
                    # Wait before next case
                    time.sleep(2)
            
            # Check if we need to go to next page
            if total_processed < max_cases:
                # Look for pagination controls
                with time_operation(self.timing_stats, 'page_navigation_times', f'navigating to page {current_page + 1}'):
                    try:
                        # Try to find next button
                        next_button = None
                        
                        # Common pagination selectors
                        pagination_selectors = [
                            "a[aria-label='Next']",
                            "button[aria-label='Next']",
                            ".pagination .next a",
                            ".pagination li.next a",
                            "a:contains('Next')",
                            "button:contains('Next')",
                            "a[title='Next']",
                            ".paginate_button.next",
                            "#report_body_next"
                        ]
                        
                        for selector in pagination_selectors:
                            try:
                                if selector.startswith('#'):
                                    element = self.driver.find_element(By.ID, selector[1:])
                                else:
                                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                                
                                if element.is_displayed() and element.is_enabled():
                                    # Check if it's not disabled
                                    classes = element.get_attribute('class') or ''
                                    if 'disabled' not in classes:
                                        next_button = element
                                        logger.info(f"Found next button with selector: {selector}")
                                        break
                            except:
                                continue
                        
                        click_sucess = False
                        logger.info(f"Clicking next button...")
                        count = 0
                        while (next_button) and (not click_sucess) and (count < 5):
                            try:
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", next_button)
                                time.sleep(5)
                                next_button.click()
                                click_sucess = True
                                time.sleep(10)  # Wait for page to load
                                current_page += 1
                            except Exception as e:
                                count+=1
                                screenshot_path = os.path.join(self.output_dir, "screenshots", f"next_page_error_{current_page}.png")
                                self.driver.save_screenshot(screenshot_path)
                            
                    except Exception as e:
                        logger.error(f"Error navigating to next page: {e}")
                        break
        
        # End overall timing
        self.timing_stats.end_overall()
        
        # Check for new files
        final_files = set(os.listdir(pdf_dir)) if os.path.exists(pdf_dir) else set()
        new_files = list(final_files - initial_files)
        
        # Save timing stats
        timing_file = os.path.join(self.output_dir, "logs", f"timing_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        self.timing_stats.save_to_file(timing_file)
        logger.info(f"Timing statistics saved to: {timing_file}")
        
        # Print timing summary
        self.timing_stats.print_summary()
        
        return {
            'total_cases': len(results),
            'results': results,
            'new_files': new_files,
            'pages_processed': current_page,
            'timestamp': datetime.now().isoformat(),
            'timing_stats': self.timing_stats.get_summary()
        }


def main():
    """Test the overlay PDF downloader"""
    print("\n=== eCourts Overlay PDF Downloader ===")
    print("This handles PDFs that open in an overlay/modal on the same page\n")
    
    # Try to load configuration
    captcha_solver = None
    try:
        import config
        
        # Set up captcha solver based on configuration
        if hasattr(config, 'CAPTCHA_SERVICE') and config.CAPTCHA_SERVICE:
            if config.CAPTCHA_SERVICE.lower() == "2captcha" and hasattr(config, 'TWOCAPTCHA_API_KEY'):
                captcha_solver = TwoCaptchaSolver(config.TWOCAPTCHA_API_KEY)
                print("Using 2captcha service for automatic captcha solving")
            elif config.CAPTCHA_SERVICE.lower() == "anticaptcha" and hasattr(config, 'ANTICAPTCHA_API_KEY'):
                captcha_solver = AntiCaptchaSolver(config.ANTICAPTCHA_API_KEY)
                print("Using Anti-captcha service for automatic captcha solving")
        
        # Use config values if available
        output_dir = getattr(config, 'DEFAULT_OUTPUT_DIR', 'overlay_pdf_downloads')
        headless = getattr(config, 'DEFAULT_HEADLESS', True)
        
    except ImportError:
        print("No config.py found. Using default settings.")
        print("To enable automatic captcha solving, create config.py from config_example.py")
        output_dir = "overlay_pdf_downloads"
        headless = True
    
    # Get number of cases from user
    while True:
        try:
            max_cases = int(input("\nEnter the number of cases to download: "))
            if max_cases > 0:
                break
            else:
                print("Please enter a positive number.")
        except ValueError:
            print("Please enter a valid number.")
    
    # Ask about headless mode if not using config
    if 'config' not in locals():
        use_headless = input("\nRun in headless mode? (y/n, default: y): ").lower().strip()
        headless = use_headless != 'n'
    
    print(f"\nSettings:")
    print(f"  Output directory: {output_dir}")
    print(f"  Headless mode: {headless}")
    print(f"  Captcha solver: {captcha_solver.__class__.__name__ if captcha_solver else 'Manual'}")
    print(f"  Cases to download: {max_cases}")
    
    downloader = ECourtsOverlayPDFDownloader(
        output_dir=output_dir,
        headless=headless,
        captcha_solver=captcha_solver
    )
    
    try:
        results = downloader.download_pdfs_from_search(max_cases=max_cases)
        
        print(f"\n{'='*50}")
        print("RESULTS SUMMARY")
        print(f"{'='*50}")
        
        print(f"\nTotal cases processed: {results.get('total_cases', 0)}")
        print(f"Pages processed: {results.get('pages_processed', 1)}")
        
        success_count = sum(1 for r in results.get('results', []) if r['success'])
        print(f"Successful downloads: {success_count}")
        print(f"Failed downloads: {results.get('total_cases', 0) - success_count}")
        
        print("\nDetailed Results:")
        for result in results.get('results', []):
            print(f"\nCase: {result['case_number']}")
            print(f"  Success: {result['success']}")
            if result.get('method'):
                print(f"  Method: {result['method']}")
            if result.get('download_time'):
                print(f"  Download Time: {result['download_time']:.2f} seconds")
            if result.get('error'):
                print(f"  Error: {result['error']}")
        
        if results.get('new_files'):
            print(f"\nNew files downloaded:")
            for file in results['new_files']:
                print(f"  - {file}")
        
        # Timing stats are already printed by the downloader
        
    finally:
        downloader.close_driver()
    
    print("\n=== Test Complete ===")


if __name__ == "__main__":
    main()
