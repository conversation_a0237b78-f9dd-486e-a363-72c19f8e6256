"""
Configuration example for eCourts scraper
Copy this file to config.py and add your API keys
"""

# Captcha solving service configuration
CAPTCHA_SERVICE = "2captcha"  # Options: "2captcha", "anticaptcha", None

# API Keys (get these from the respective services)
TWOCAPTCHA_API_KEY = "557a2a66969ec1ed2848b1d6ca4cf231"
ANTICAPTCHA_API_KEY = "your_anticaptcha_api_key_here"

# Scraper settings
DEFAULT_OUTPUT_DIR = "overlay_pdf_downloads"
DEFAULT_HEADLESS = True
DEFAULT_SEARCH_KEYWORD = "GST"
DEFAULT_COURT_TYPE = "High Court"
DEFAULT_SEARCH_TYPE = "phrase"  # Options: "phrase", "any_words", "all_words"

# Resume point for failed downloads
RESUME_FROM_CASE = 0
