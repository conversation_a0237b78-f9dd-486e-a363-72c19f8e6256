"""
Captcha solving services for automated captcha resolution
"""

import time
import logging
import requests
import base64
from typing import Optional

logger = logging.getLogger(__name__)


class CaptchaSolver:
    """Base class for captcha solving services"""
    
    def solve_image_captcha(self, image_path: str) -> Optional[str]:
        """Solve captcha from image file"""
        raise NotImplementedError("Subclass must implement solve_image_captcha")


class TwoCaptchaSolver(CaptchaSolver):
    """2captcha.com solver implementation"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "http://2captcha.com"
        
    def solve_image_captcha(self, image_path: str) -> Optional[str]:
        """Solve captcha using 2captcha service"""
        try:
            # Read image and encode to base64
            with open(image_path, 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')
            
            # Submit captcha
            submit_url = f"{self.base_url}/in.php"
            submit_data = {
                'key': self.api_key,
                'method': 'base64',
                'body': image_data,
                'json': 1,
                'regsense': 1
            }
            
            response = requests.post(submit_url, data=submit_data)
            result = response.json()
            
            if result.get('status') != 1:
                logger.error(f"2captcha submit error: {result.get('error_text', 'Unknown error')}")
                return None
                
            captcha_id = result.get('request')
            logger.info(f"Captcha submitted, ID: {captcha_id}")
            
            # Wait and check for result
            result_url = f"{self.base_url}/res.php"
            max_attempts = 30  # 30 attempts with 2 second delays = 60 seconds max
            
            for attempt in range(max_attempts):
                time.sleep(2)
                
                result_data = {
                    'key': self.api_key,
                    'action': 'get',
                    'id': captcha_id,
                    'json': 1
                }
                
                response = requests.get(result_url, params=result_data)
                result = response.json()
                
                if result.get('status') == 1:
                    captcha_text = result.get('request')
                    logger.info(f"Captcha solved: {captcha_text}")
                    return captcha_text
                elif result.get('request') == 'CAPCHA_NOT_READY':
                    continue
                else:
                    logger.error(f"2captcha error: {result.get('error_text', 'Unknown error')}")
                    return None
                    
            logger.error("Captcha solving timeout")
            return None
            
        except Exception as e:
            logger.error(f"Error solving captcha: {e}")
            return None


class AntiCaptchaSolver(CaptchaSolver):
    """Anti-captcha.com solver implementation"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.anti-captcha.com"
        
    def solve_image_captcha(self, image_path: str) -> Optional[str]:
        """Solve captcha using Anti-captcha service"""
        try:
            # Read image and encode to base64
            with open(image_path, 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')
            
            # Create task
            create_task_url = f"{self.base_url}/createTask"
            task_data = {
                "clientKey": self.api_key,
                "task": {
                    "type": "ImageToTextTask",
                    "body": image_data,
                    "case": True,  # Case sensitive
                    "numeric": 0,  # Any characters
                    "math": False
                }
            }
            
            response = requests.post(create_task_url, json=task_data)
            result = response.json()
            
            if result.get('errorId') != 0:
                logger.error(f"Anti-captcha error: {result.get('errorDescription', 'Unknown error')}")
                return None
                
            task_id = result.get('taskId')
            logger.info(f"Task created, ID: {task_id}")
            
            # Wait for result
            get_result_url = f"{self.base_url}/getTaskResult"
            max_attempts = 30
            
            for attempt in range(max_attempts):
                time.sleep(2)
                
                result_data = {
                    "clientKey": self.api_key,
                    "taskId": task_id
                }
                
                response = requests.post(get_result_url, json=result_data)
                result = response.json()
                
                if result.get('errorId') != 0:
                    logger.error(f"Anti-captcha error: {result.get('errorDescription', 'Unknown error')}")
                    return None
                    
                if result.get('status') == 'ready':
                    captcha_text = result.get('solution', {}).get('text')
                    logger.info(f"Captcha solved: {captcha_text}")
                    return captcha_text
                    
            logger.error("Captcha solving timeout")
            return None
            
        except Exception as e:
            logger.error(f"Error solving captcha: {e}")
            return None


def create_captcha_solver(service_name: str, api_key: str) -> Optional[CaptchaSolver]:
    """Factory function to create appropriate captcha solver"""
    if service_name.lower() == "2captcha":
        return TwoCaptchaSolver(api_key)
    elif service_name.lower() == "anticaptcha":
        return AntiCaptchaSolver(api_key)
    else:
        logger.warning(f"Unknown captcha service: {service_name}")
        return None
