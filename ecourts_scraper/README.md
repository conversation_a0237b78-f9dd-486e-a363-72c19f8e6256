# eCourts Overlay PDF Downloader

A Selenium-based web scraper for downloading PDF judgments from the eCourts website with automatic captcha solving support.

## Features

- **Bulk PDF Downloads**: Download multiple PDFs from search results
- **Pagination Support**: Automatically navigates through multiple pages
- **Modal/Overlay Handling**: Extracts PDFs from temporary URLs in modal viewers
- **Automatic Captcha Solving**: Integrates with 2captcha and Anti-captcha services
- **Headless Mode**: Can run without displaying browser window
- **Organized Output**: Creates structured directories for PDFs and screenshots
- **Error Handling**: Robust error handling with detailed logging
- **Failure Protection**: Automatically stops after 15 consecutive failures
- **Resume Capability**: Can resume downloads from where they stopped
- **Fresh Start**: Automatically performs a new search every 25 downloads to maintain stability

## Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Install Chrome browser (if not already installed)

## Project Structure

```
ecourts_scraper/
├── ecourts_overlay_pdf_downloader.py  # Main scraper class
├── captcha_solvers.py                 # Captcha solving services
├── config_example.py                  # Configuration template
├── config.py                          # Your configuration (create from example)
├── requirements.txt                   # Python dependencies
└── README.md                          # This file
```

## Configuration

### Manual Captcha Mode (Default)
Run the script without any configuration - you'll be prompted to enter captchas manually.

### Automatic Captcha Solving
1. Copy `config_example.py` to `config.py`:
```bash
cp config_example.py config.py
```

2. Edit `config.py` and add your API key:
```python
# For 2captcha.com
CAPTCHA_SERVICE = "2captcha"
TWOCAPTCHA_API_KEY = "your_actual_api_key_here"

# OR for anti-captcha.com
CAPTCHA_SERVICE = "anticaptcha"
ANTICAPTCHA_API_KEY = "your_actual_api_key_here"
```

3. Get API keys from:
   - 2captcha: https://2captcha.com
   - Anti-captcha: https://anti-captcha.com

## Usage

### Basic Usage
```bash
python ecourts_overlay_pdf_downloader.py
```

You'll be prompted for:
- Number of cases to download
- Headless mode preference (if no config.py)

### With Configuration
If you have a `config.py` file, the script will use those settings automatically.

### Output Structure
```
overlay_pdf_downloads/
├── pdfs/                    # Downloaded PDF files
├── screenshots/             # Screenshots of overlays
├── captcha_*.png           # Captcha images (for manual solving)
├── debug_page.html         # Debug HTML (if errors occur)
└── no_results_screenshot.png # Screenshot when no results found
```

## Captcha Services

### 2captcha
- Pricing: ~$3 per 1000 captchas
- API Documentation: https://2captcha.com/api-docs
- Average solving time: 15-30 seconds

### Anti-captcha
- Pricing: ~$2 per 1000 captchas
- API Documentation: https://anti-captcha.com/apidoc
- Average solving time: 10-20 seconds

## Advanced Usage

### Custom Integration
```python
from ecourts_overlay_pdf_downloader import ECourtsOverlayPDFDownloader
from captcha_solvers import TwoCaptchaSolver, AntiCaptchaSolver

# Create captcha solver
solver = TwoCaptchaSolver("your_api_key")
# or
# solver = AntiCaptchaSolver("your_api_key")

# Create downloader with solver
downloader = ECourtsOverlayPDFDownloader(
    output_dir="my_pdfs",
    headless=True,
    captcha_solver=solver
)

# Download PDFs
results = downloader.download_pdfs_from_search(max_cases=50)
```

### Using the Factory Function
```python
from ecourts_overlay_pdf_downloader import ECourtsOverlayPDFDownloader
from captcha_solvers import create_captcha_solver

# Create solver using factory function
solver = create_captcha_solver("2captcha", "your_api_key")

# Use with downloader
downloader = ECourtsOverlayPDFDownloader(
    output_dir="my_pdfs",
    headless=True,
    captcha_solver=solver
)
```

## Troubleshooting

### Headless Mode Issues
- Some sites detect headless browsers
- Try running without headless mode if downloads fail
- Check `search_results_debug.png` for debugging

### Captcha Solving Failures
- Verify API key is correct
- Check account balance on captcha service
- Ensure captcha image is clear (check saved captcha images)

### Download Failures
- Check `screenshots/` directory for overlay screenshots
- Verify PDFs are actually displayed in the modal
- Some PDFs might be restricted or require login

## Notes

- The script waits 20 seconds after search submission for results to load
- Each PDF download has a 1-second timeout
- Modal closing is attempted after each download
- The script handles pagination automatically
- Captcha solving services are modularized in `captcha_solvers.py`
- Automatically stops after 15 consecutive download failures
- Resume point is saved to config.py when stopped due to failures
- Automatically performs a fresh search every 25 successful downloads (includes new captcha)

## Resume Functionality

If the script stops due to consecutive failures:

1. The resume point is automatically saved to `config.py`:
   ```python
   RESUME_FROM_CASE = 123  # Example: resume from case 123
   ```

2. On the next run, the script will:
   - Skip all cases before the resume point
   - Start downloading from the saved case number
   - Continue until completion or another failure limit

3. To start fresh (ignore resume point):
   - Remove or comment out `RESUME_FROM_CASE` from config.py
   - Or set it to 0: `RESUME_FROM_CASE = 0`

## License

This tool is for educational and research purposes. Ensure you comply with the website's terms of service and robots.txt.
