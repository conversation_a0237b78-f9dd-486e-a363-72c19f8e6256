# Swiggy Data Scraper

A comprehensive Python-based web scraper for extracting restaurant data, menu items, prices, and other information from Swiggy.com.

## Features

- **Restaurant Data Extraction**: Extract restaurant names, ratings, cuisines, delivery times, and costs
- **Menu Item Scraping**: Get detailed menu items with prices and descriptions
- **Area-based Search**: Search restaurants in specific areas/localities
- **Search Functionality**: Search for restaurants by name or cuisine type
- **Multiple Output Formats**: Save data in JSON or CSV format
- **Anti-Detection Features**: Rotate user agents and implement delays to avoid blocking
- **Headless Mode**: Run browser in background for automated scraping
- **Error Handling**: Robust error handling with retry mechanisms
- **Selenium Integration**: Handle dynamic content and JavaScript-rendered pages

## Requirements

- Python 3.8+
- Chrome browser (for Selenium WebDriver)
- Required Python packages (see `swiggy_requirements.txt`)

## Installation

1. **Install Python dependencies:**
   ```bash
   pip install -r swiggy_requirements.txt
   ```

2. **Install Chrome browser** (if not already installed)
   - The scraper will automatically download ChromeDriver using webdriver-manager

## Usage

### Basic Usage

```bash
# Scrape restaurants in Koramangala, Bangalore
python swiggy_scraper.py --city bangalore --area koramangala

# Scrape restaurants in a different city and area
python swiggy_scraper.py --city mumbai --area bandra --limit 30

# Search for specific cuisine or restaurant
python swiggy_scraper.py --search "pizza" --city delhi --area cp

# Save output as CSV
python swiggy_scraper.py --format csv --city pune --area koregaon-park
```

### Advanced Usage

```bash
# Scrape with menu items (slower but more detailed)
python swiggy_scraper.py --menu --limit 10 --city bangalore --area indiranagar

# Run in headless mode (no browser window)
python swiggy_scraper.py --headless --city chennai --area t-nagar

# Custom output filename
python swiggy_scraper.py --output my_restaurants --city hyderabad --area hitech-city
```

### Command-line Options

- `--city`: City to scrape (default: bangalore)
- `--area`: Area/locality to scrape (default: koramangala)
- `--limit`: Number of restaurants to scrape (default: 20)
- `--search`: Search for specific restaurants or cuisine
- `--format`: Output format - json or csv (default: json)
- `--output`: Output file prefix (default: swiggy_data)
- `--headless`: Run browser in headless mode
- `--menu`: Also scrape menu items (slower)

## Available Cities

The scraper supports major Indian cities including:
- Bangalore (bangalore)
- Mumbai (mumbai)
- Delhi (delhi)
- Pune (pune)
- Chennai (chennai)
- Hyderabad (hyderabad)
- Kolkata (kolkata)
- Ahmedabad (ahmedabad)
- And many more...

## Output Format

### Restaurant Data

The scraper extracts the following data for each restaurant:

- `name`: Restaurant name
- `rating`: Customer rating
- `cuisine`: Type of cuisine served
- `delivery_time`: Estimated delivery time
- `cost_for_two`: Cost for two people
- `url`: Restaurant URL on Swiggy
- `area`: Location/area of the restaurant

### Menu Items (when --menu flag is used)

For each menu item, the following data is extracted:

- `name`: Item name
- `price`: Item price
- `description`: Item description
- `category`: Menu category

### Example JSON Output

```json
[
  {
    "name": "Pizza Hut",
    "rating": "4.2",
    "cuisine": "Pizzas, Italian, Desserts",
    "delivery_time": "25-30 mins",
    "cost_for_two": "₹350 for two",
    "url": "https://www.swiggy.com/restaurants/pizza-hut-koramangala-bangalore-123456",
    "area": "Koramangala",
    "menu_items": [
      {
        "name": "Margherita Pizza",
        "price": "₹199",
        "description": "Classic pizza with tomato sauce and mozzarella",
        "category": "Pizzas"
      }
    ]
  }
]
```

## Programming Interface

You can also use the scraper programmatically:

```python
from swiggy_scraper import SwiggyScraper

# Initialize scraper
scraper = SwiggyScraper(city="bangalore", headless=True)

try:
    # Get restaurants in an area
    restaurants = scraper.get_restaurants_in_area("koramangala", limit=10)
    
    # Search for specific restaurants
    pizza_places = scraper.search_restaurants("pizza", area="indiranagar")
    
    # Get menu items for a specific restaurant
    if restaurants and restaurants[0].get('url'):
        menu_items = scraper.get_menu_items(restaurants[0]['url'])
    
    # Save data
    scraper.save_to_json(restaurants, "restaurants.json")
    scraper.save_to_csv(restaurants, "restaurants.csv")
    
finally:
    scraper.close_driver()
```

## Technical Details

### Scraping Strategy

1. **Selenium WebDriver**: Used for handling JavaScript-rendered content
2. **Dynamic Content Loading**: Implements scrolling to load more restaurants
3. **Anti-Detection**: Rotates user agents and implements random delays
4. **Error Handling**: Retry mechanisms for failed requests
5. **Data Extraction**: Multiple CSS selector fallbacks for reliable data extraction

### Rate Limiting

The scraper implements several rate limiting strategies:
- Random delays between requests (2-3 seconds by default)
- Jitter added to prevent predictable timing patterns
- Exponential backoff for failed requests
- User agent rotation

## Ethical Usage

Please use this scraper responsibly:

1. **Respect Rate Limits**: Use appropriate delays between requests
2. **Limit Concurrent Requests**: Don't run multiple instances simultaneously
3. **Terms of Service**: Comply with Swiggy's terms of service
4. **Data Usage**: Use scraped data for personal/research purposes only
5. **Off-Peak Hours**: Consider running during off-peak hours

## Troubleshooting

### Common Issues

1. **Chrome Driver Issues**
   - The scraper automatically downloads ChromeDriver
   - Ensure Chrome browser is installed and updated

2. **Element Not Found Errors**
   - Website structure may have changed
   - Try increasing delays between requests
   - Check if the area/city name is correct

3. **No Restaurants Found**
   - Verify the city and area names are correct
   - Some areas might not be available on Swiggy
   - Try different search terms

4. **Timeout Errors**
   - Increase delay between requests
   - Check internet connection
   - Try running in non-headless mode for debugging

### Debug Mode

Run without headless mode to see what's happening:

```bash
python swiggy_scraper.py --city bangalore --area koramangala
```

## Output Directory

All output files are saved in the `output/` directory with timestamped filenames:
- Format: `swiggy_data_{city}_{area}_{timestamp}.{format}`
- Example: `swiggy_data_bangalore_koramangala_20250611_143022.json`

## Contributing

Feel free to contribute by:
- Reporting bugs
- Suggesting new features
- Improving scraping selectors
- Adding support for new cities
- Enhancing error handling

## License

This project is for educational and research purposes only. Please respect Swiggy's terms of service and use responsibly.

## Disclaimer

This scraper is provided as-is for educational purposes. The authors are not responsible for any misuse or violations of terms of service. Always ensure you have permission to scrape websites and comply with their robots.txt and terms of service.
