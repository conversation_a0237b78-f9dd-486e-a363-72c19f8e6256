#!/bin/bash

# Swiggy Scraper Setup Script
echo "🍕 Setting up <PERSON>wig<PERSON> Scraper..."
echo "================================"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8+ first."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Create virtual environment (optional but recommended)
echo "📦 Creating virtual environment..."
python3 -m venv swiggy_env 2>/dev/null || echo "⚠️  Virtual environment already exists or couldn't be created"

# Activate virtual environment
if [ -d "swiggy_env" ]; then
    echo "🔄 Activating virtual environment..."
    source swiggy_env/bin/activate
    echo "✅ Virtual environment activated"
else
    echo "⚠️  Proceeding without virtual environment"
fi

# Install requirements
echo "📥 Installing Python packages..."
pip3 install -r swiggy_requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ All packages installed successfully!"
else
    echo "❌ Failed to install some packages. Please check the error messages above."
    exit 1
fi

# Check if Chrome is installed
if command -v google-chrome &> /dev/null || command -v google-chrome-stable &> /dev/null || command -v chromium-browser &> /dev/null; then
    echo "✅ Chrome browser found"
else
    echo "⚠️  Chrome browser not found. Please install Google Chrome:"
    echo "   - macOS: Download from https://www.google.com/chrome/"
    echo "   - Ubuntu: sudo apt install google-chrome-stable"
    echo "   - Other Linux: Install chromium-browser"
fi

# Create output directory
mkdir -p output
echo "✅ Output directory created"

# Make scripts executable
chmod +x swiggy_scraper.py
chmod +x swiggy_examples.py
echo "✅ Scripts made executable"

echo ""
echo "🎉 Setup complete!"
echo "================================"
echo ""
echo "📖 Quick Start Guide:"
echo "1. Basic usage:"
echo "   python3 swiggy_scraper.py --city bangalore --area koramangala"
echo ""
echo "2. Search for restaurants:"
echo "   python3 swiggy_scraper.py --search pizza --city mumbai"
echo ""
echo "3. Run examples:"
echo "   python3 swiggy_examples.py"
echo ""
echo "4. Get help:"
echo "   python3 swiggy_scraper.py --help"
echo ""
echo "📁 Output files will be saved in the 'output/' directory"
echo ""
echo "⚠️  Important Notes:"
echo "- Use responsibly and respect Swiggy's terms of service"
echo "- Add delays between requests to avoid being blocked"
echo "- The scraper may need updates if Swiggy changes their website structure"
echo ""
echo "Happy scraping! 🚀"
