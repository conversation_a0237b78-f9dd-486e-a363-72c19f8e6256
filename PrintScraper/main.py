#!/usr/bin/env python3
"""
ThePrint News Scraper - Main Entry Point
This script provides a command-line interface for scraping articles from ThePrint website.
"""

import argparse
import logging
import os
import sys
from datetime import datetime

from scraper import ThePrintScraper
from utils import setup_logging

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Scrape news articles from ThePrint website')
    
    parser.add_argument('-o', '--output', type=str, default='output', 
                        help='Output directory for scraped data (default: output)')
    parser.add_argument('-f', '--format', type=str, choices=['json', 'csv'], default='json',
                        help='Output format (json or csv)')
    parser.add_argument('-p', '--pages', type=int, default=10,
                        help='Number of pages to scrape (default: 1)')
    parser.add_argument('-c', '--category', type=str, default='india',
                        help='Category to scrape (e.g., india, opinion, etc.)')
    parser.add_argument('-d', '--delay', type=float, default=2.0,
                        help='Delay between requests in seconds (default: 2.0)')
    parser.add_argument('-r', '--retries', type=int, default=3,
                        help='Number of retry attempts for failed requests (default: 3)')
    parser.add_argument('-v', '--verbose', action='store_true',
                        help='Enable verbose output')
    
    return parser.parse_args()

def main():
    """Main function"""
    args = parse_arguments()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    setup_logging(log_level)
    
    logger = logging.getLogger(__name__)
    logger.info("Starting ThePrint scraper")
    
    # Print banner
    print("\n" + "=" * 50)
    print(f"  ThePrint News Scraper")
    print("=" * 50)
    print(f"  Target Website: https://theprint.in")
    if args.category:
        print(f"  Category: {args.category}")
    else:
        print(f"  Category: All (main page)")
    print(f"  Pages to scrape: {args.pages}")
    print(f"  Request delay: {args.delay} seconds")
    print(f"  Output format: {args.format.upper()}")
    print(f"  Output directory: {args.output}")
    print("=" * 50 + "\n")
    
    # Ensure output directory exists
    os.makedirs(args.output, exist_ok=True)
    
    try:
        # Initialize and run the scraper
        print(f"Initializing scraper...")
        scraper = ThePrintScraper(
            category=args.category,
            delay=args.delay,
            max_retries=args.retries
        )
        
        # Start the scraping process
        print(f"Starting to scrape {args.pages} page(s)...")
        start_time = datetime.now()
        articles = scraper.scrape_multiple_pages(max_pages=args.pages)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if not articles:
            logger.warning("No articles found!")
            print("\nNo articles found! The website structure might have changed or there might be access restrictions.")
            print("Try using a different category or increasing the delay between requests.")
            return 1
        
        # Create a timestamped filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        category_str = args.category if args.category else "all"
        filename = f"theprint_{category_str}_{timestamp}"
        
        # Save the data
        output_path = os.path.join(args.output, filename)
        print(f"\nSaving {len(articles)} articles to {output_path}.{args.format}...")
        
        if args.format == 'json':
            scraper.save_to_json(articles, f"{output_path}.json")
            logger.info(f"Data saved to {output_path}.json")
        else:  # csv
            scraper.save_to_csv(articles, f"{output_path}.csv")
            logger.info(f"Data saved to {output_path}.csv")
            
        logger.info(f"Successfully scraped {len(articles)} articles")
        
        # Print summary
        print("\n" + "=" * 50)
        print("  Scraping Summary")
        print("=" * 50)
        print(f"  Articles scraped: {len(articles)}")
        print(f"  Time taken: {duration:.2f} seconds")
        print(f"  Average time per article: {duration/len(articles):.2f} seconds" if articles else "  Average time per article: N/A")
        print(f"  Output file: {output_path}.{args.format}")
        print("=" * 50 + "\n")
        
        print(f"Scraping completed successfully!")
        return 0
        
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
        print("\nScraping interrupted by user")
        return 130
    except Exception as e:
        logger.error(f"Error during scraping: {e}", exc_info=args.verbose)
        print(f"\nError during scraping: {e}")
        print("See logs for more details")
        return 1

if __name__ == "__main__":
    sys.exit(main())
