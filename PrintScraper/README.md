# ThePrint News Scraper

A Python-based web scraper for extracting news articles from ThePrint website (https://theprint.in).

## Features

- Scrapes news articles from ThePrint website
- Extracts article titles, authors, dates, and content
- Handles pagination to access multiple pages of articles
- Processes and stores scraped data in structured format (CSV or JSON)
- Implements error handling and retry mechanism
- Uses rate limiting to avoid overloading the target website
- Advanced anti-detection features (cookie handling, user-agent rotation)
- Intelligent content extraction for best results

## Requirements

- Python 3.6+
- Required libraries:
  - requests
  - beautifulsoup4 
  - trafilatura

## Installation

```bash
# Clone the repository
git clone [repository-url]
cd theprint-scraper

# Install dependencies
pip install requests beautifulsoup4 trafilatura
```

## Usage

The scraper can be run from the command line with various options to customize the scraping process.

### Basic Usage

```bash
# Scrape the main page (1 page)
python main.py

# Scrape a specific category (e.g., opinion)
python main.py --category opinion

# Scrape multiple pages
python main.py --pages 3

# Save output as CSV instead of JSON
python main.py --format csv
```

### Command-line Options

| Option | Short | Description | Default |
|--------|-------|-------------|---------|
| `--output` | `-o` | Output directory for scraped data | `output` |
| `--format` | `-f` | Output format (json or csv) | `json` |
| `--pages` | `-p` | Number of pages to scrape | `1` |
| `--category` | `-c` | Category to scrape (e.g., india, opinion, etc.) | Empty (main page) |
| `--delay` | `-d` | Delay between requests in seconds | `2.0` |
| `--retries` | `-r` | Number of retry attempts for failed requests | `3` |
| `--verbose` | `-v` | Enable verbose output | Disabled |

### Examples

Scrape 5 pages from the "opinion" category with verbose output:

```bash
python main.py --category opinion --pages 5 --verbose
```

Scrape the main page with a custom delay and save as CSV:

```bash
python main.py --delay 3.5 --format csv
```

## Available Categories

ThePrint website organizes content into several categories. Here are some common ones:

- `india` - News about India
- `opinion` - Opinion articles
- `politics` - Political news
- `economy` - Economy and business news
- `defence` - Defense and security news
- `health` - Health-related news
- `diplomacy` - International relations
- `world` - World news
- `judiciary` - Legal and judicial news

## Output Format

The scraper extracts the following data for each article:

- `title`: Article title
- `url`: Article URL
- `author`: Article author (if available)
- `date`: Publication date (if available)
- `content`: Full article text content

Data is saved in the specified output directory with a timestamped filename in the format: `theprint_CATEGORY_YYYYMMDD_HHMMSS.FORMAT`

## Ethical Usage

Please use this scraper responsibly:

1. Set reasonable delays between requests (`--delay` option)
2. Limit the number of pages scraped in a single session
3. Respect the website's terms of service and robots.txt
4. Consider using the scraper during off-peak hours
5. Only use the scraped data for personal or research purposes

## Troubleshooting

If you encounter issues:

1. Increase the delay between requests (`--delay` option)
2. Try a different category
3. Enable verbose output (`--verbose`) for detailed logs
4. Check if the website structure has changed
5. Ensure you have the latest version of the required libraries
