#!/bin/bash

# ThePrint News Scraper Demo Script
echo -e "\n\033[1m===== ThePrint News Scraper Demo =====\033[0m"

# Display existing sample outputs
echo -e "\n\033[1mSample outputs available:\033[0m"
ls -l output/

# Show sample JSON content
echo -e "\n\033[1mSample JSON output (first article title and author):\033[0m"
echo "Title: $(grep -o '"title": "[^"]*"' output/theprint_india_20250403_140300.json | head -1 | cut -d'"' -f4)"
echo "Author: $(grep -o '"author": "[^"]*"' output/theprint_india_20250403_140300.json | head -1 | cut -d'"' -f4)"
echo "URL: $(grep -o '"url": "[^"]*"' output/theprint_india_20250403_140300.json | head -1 | cut -d'"' -f4)"

# Show sample CSV content
echo -e "\n\033[1mSample CSV output (first few lines):\033[0m"
head -n 4 output/theprint_india_20250403_140330.csv

# Demonstrate available scraping options
echo -e "\n\033[1mAvailable scraping options:\033[0m"
python main.py --help

echo -e "\n\033[1mScrapers ready to use with different parameters:\033[0m"
echo "python main.py --category india --pages 1 --delay 5"
echo "python main.py --category opinion --pages 2 --format csv"
echo "python main.py --category world --pages 1 --verbose"

echo -e "\n\033[1m===== End of Demo =====\033[0m"