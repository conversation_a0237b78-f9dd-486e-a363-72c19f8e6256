"""
ThePrint News Scraper - Core Scraping Module
This module contains the main scraping functionality for ThePrint website.
"""

import csv
import json
import logging
import random
import time
from typing import Dict, List, Optional, Union

import requests
from bs4 import BeautifulSoup
import trafilatura

# Configure logger
logger = logging.getLogger(__name__)

class ThePrintScraper:
    """Scraper for ThePrint news website"""
    
    BASE_URL = "https://theprint.in"
    
    # Common user agents to rotate for requests
    USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
        "Mozilla/5.0 (X11; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
    ]
    
    def __init__(self, category: str = "", delay: float = 2.0, max_retries: int = 3):
        """
        Initialize the ThePrint scraper
        
        Args:
            category: The category to scrape (e.g., 'india', 'opinion', etc.)
            delay: Delay between requests in seconds
            max_retries: Maximum number of retry attempts for failed requests
        """
        self.category = category
        self.delay = delay
        self.max_retries = max_retries
        
        # Build the starting URL based on category
        if category:
            self.start_url = f"{self.BASE_URL}/category/{category}/"
        else:
            self.start_url = self.BASE_URL
            
        logger.info(f"Initialized scraper with start URL: {self.start_url}")
    
    def _get_headers(self) -> Dict[str, str]:
        """Generate random headers for HTTP requests"""
        return {
            "User-Agent": random.choice(self.USER_AGENTS),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Cache-Control": "max-age=0",
        }
    
    def _make_request(self, url: str) -> Optional[requests.Response]:
        """
        Make an HTTP request with retry logic and rate limiting
        
        Args:
            url: URL to request
            
        Returns:
            Response object or None if all requests fail
        """
        for attempt in range(self.max_retries):
            try:
                # Add jitter to delay to prevent detection
                jitter = random.uniform(-0.5, 0.5)
                sleep_time = max(0.5, self.delay + jitter)
                
                # Sleep before making the request (except for the first attempt)
                if attempt > 0:
                    time.sleep(sleep_time * attempt)  # Increase delay with each retry
                else:
                    time.sleep(sleep_time)
                
                logger.debug(f"Requesting URL: {url}")
                
                # Some websites block requests without proper referrer and cookies
                headers = self._get_headers()
                headers['Referer'] = self.BASE_URL
                
                # Use a session to maintain cookies
                session = requests.Session()
                session.headers.update(headers)
                
                # First visit the homepage to get cookies
                if attempt == 0:
                    try:
                        logger.debug(f"Visiting homepage to get cookies")
                        session.get(self.BASE_URL, timeout=10)
                    except:
                        pass  # Ignore errors here, still try the main request
                
                # Make the actual request
                response = session.get(url, timeout=30)
                
                # Check for successful response
                if response.status_code == 200:
                    # Debug - log a small sample of the response to check if it's HTML
                    sample = response.text[:200] if len(response.text) > 200 else response.text
                    logger.debug(f"Response sample: {sample}")
                    
                    # Check for common bot detection patterns
                    if "captcha" in response.text.lower() or "blocked" in response.text.lower() or "security check" in response.text.lower():
                        logger.warning("Bot detection detected in response, retrying with different user agent")
                        continue
                        
                    return response
                
                logger.warning(f"Request failed with status code {response.status_code}, attempt {attempt + 1}/{self.max_retries}")
                
            except requests.RequestException as e:
                logger.warning(f"Request error: {e}, attempt {attempt + 1}/{self.max_retries}")
                
            # Increase delay with each retry to avoid rate limiting
            time.sleep(attempt * 2)
        
        logger.error(f"Failed to retrieve {url} after {self.max_retries} attempts")
        return None
    
    def get_article_links(self, page_url: str) -> List[str]:
        """
        Extract article links from a page
        
        Args:
            page_url: URL of the page to extract links from
            
        Returns:
            List of article URLs
        """
        response = self._make_request(page_url)
        if not response:
            return []
        
        soup = BeautifulSoup(response.text, 'html.parser')
        links = []
        
        # Debug HTML structure
        logger.debug(f"Fetched page content length: {len(response.text)}")
        
        # Identify if we're on a category page or main page
        is_category_page = "/category/" in page_url
        logger.debug(f"Is category page: {is_category_page}")
        
        # Enhanced selectors for ThePrint articles that target multiple potential layouts
        selectors = [
            'article.categoryPage__story', 
            'div.storyCard', 
            'div.m-storyCard', 
            'div.story-card',
            'div.stories-card', 
            'div.story',
            'div.article-card',
            'div.td_module_10',
            'div.td_module_16',
            'div.post',
            'div.entry',
            'li.article-entry',
            'div.card'
        ]
        
        # For each selector, try to find articles
        for selector in selectors:
            elements = soup.select(selector)
            if elements:
                logger.debug(f"Found {len(elements)} elements with selector: {selector}")
                for element in elements:
                    link_element = element.find('a')
                    if link_element and hasattr(link_element, 'attrs') and 'href' in link_element.attrs:
                        url = link_element['href']
                        # Skip if not a valid URL
                        if not url or url.startswith('#') or url == '/':
                            continue
                        # Ensure the URL is absolute
                        if not url.startswith('http'):
                            url = self.BASE_URL + url if not url.startswith('/') else self.BASE_URL + url
                        if url not in links:  # Avoid duplicates
                            links.append(url)
        
        # If we still couldn't find any articles with specific selectors, use a more general approach
        if not links:
            logger.debug("No articles found with specific selectors, trying general approach")
            all_links = soup.find_all('a')
            logger.debug(f"Found {len(all_links)} total link elements")
            
            # Look for patterns that indicate an article URL
            article_patterns = [
                '/india/', '/opinion/', '/politics/', '/world/', 
                '/category/', '/business/', '/economy/', '/defence/',
                '/sport/', '/sports/', '/health/', '/diplomacy/', 
                '/tech/', '/technology/', '/education/', '/entertainment/',
                '/20' # Year pattern like 2023, 2024, etc.
            ]
            
            # Filter links with article patterns
            for link in all_links:
                url = link.get('href', '')
                # Check if the URL matches any article pattern
                is_article_url = any(pattern in url for pattern in article_patterns)
                
                if url and is_article_url:
                    # Skip category pages when collecting articles
                    if '/category/' in url and not is_category_page:
                        continue
                        
                    # Ensure the URL is absolute
                    if not url.startswith('http'):
                        url = self.BASE_URL + url if not url.startswith('/') else self.BASE_URL + url
                    
                    # Exclude common non-article URLs
                    if '/page/' in url or '/author/' in url or '/tag/' in url or '/search/' in url or '#' in url:
                        continue
                        
                    if url not in links:  # Avoid duplicates
                        links.append(url)
            
            logger.info(f"Found {len(links)} article links using general approach on {page_url}")
        else:
            logger.info(f"Found {len(links)} article links with specific selectors on {page_url}")
            
        # Filter out non-article URLs (typically these are internal pages like categories)
        filtered_links = []
        for link in links:
            # Skip URLs that are likely not article pages
            if '/category/' in link or '/tag/' in link or '/author/' in link or '/page/' in link:
                continue
            filtered_links.append(link)
            
        logger.info(f"After filtering, found {len(filtered_links)} article links on {page_url}")
        return filtered_links
    
    def extract_article_data(self, article_url: str) -> Optional[Dict[str, str]]:
        """
        Extract data from a single article
        
        Args:
            article_url: URL of the article to scrape
            
        Returns:
            Dictionary containing article data or None if extraction fails
        """
        logger.info(f"Extracting data from article: {article_url}")
        
        response = self._make_request(article_url)
        if not response:
            return None
        
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            logger.debug(f"Article HTML length: {len(response.text)}")
            
            # Extract title - expanded selector list
            title_element = soup.select_one('h1.threedots, h1.td-post-title, h1.entry-title, h1.post-title, h1')
            title = title_element.text.strip() if title_element else "No title found"
            logger.debug(f"Title: {title}")
            
            # Extract publication date - expanded selector list
            date_element = soup.select_one('time, span.td-post-date, meta[property="article:published_time"], div.article-metadata span, span.post-date')
            if date_element:
                if date_element.name == 'meta':
                    date = date_element.get('content', '').split('T')[0]
                else:
                    date = date_element.text.strip()
            else:
                # Try all meta tags that might contain date
                date_meta = soup.find('meta', {'property': 'og:updated_time'}) or \
                           soup.find('meta', {'property': 'og:publish_time'}) or \
                           soup.find('meta', {'name': 'publish-date'})
                if date_meta:
                    date = date_meta.get('content', '').split('T')[0]
                else:
                    date = "No date found"
            logger.debug(f"Date: {date}")
            
            # Extract author - expanded selector list
            author_element = soup.select_one('div.author-name, span.td-post-author-name, a.byline-link, span.author, div.author, meta[name="author"]')
            if author_element:
                if author_element.name == 'meta':
                    author = author_element.get('content', '')
                else:
                    author = author_element.text.strip()
            else:
                # Try to find author in meta tags
                author_meta = soup.find('meta', {'property': 'article:author'}) or \
                             soup.find('meta', {'name': 'author'})
                if author_meta:
                    author = author_meta.get('content', '')
                else:
                    author = "No author found"
            logger.debug(f"Author: {author}")
            
            # Extract main content using trafilatura for better text extraction
            content = trafilatura.extract(response.text)
            
            # If trafilatura fails, use a more comprehensive fallback with BeautifulSoup
            if not content:
                logger.debug("Trafilatura failed to extract content, using BeautifulSoup fallback")
                # Try multiple potential content containers
                content_elements = soup.select('div.td-post-content, div.single-body-content, article.post, div.article-content, div.entry-content, div.post-content')
                
                if content_elements:
                    # Get all paragraphs from the first matching content element
                    paragraphs = content_elements[0].find_all('p')
                    content = ' '.join(p.text.strip() for p in paragraphs)
                else:
                    # Last resort: try to get all paragraphs directly
                    all_paragraphs = soup.find_all('p')
                    # Filter out very short paragraphs that are likely navigation or UI elements
                    valid_paragraphs = [p.text.strip() for p in all_paragraphs if len(p.text.strip()) > 50]
                    content = ' '.join(valid_paragraphs) if valid_paragraphs else "No content found"
            
            logger.debug(f"Content length: {len(content) if content else 0}")
            
            return {
                'title': title,
                'url': article_url,
                'author': author,
                'date': date,
                'content': content
            }
            
        except Exception as e:
            logger.error(f"Error extracting data from {article_url}: {e}")
            return None
    
    def get_next_page_url(self, current_url: str, page_number: int) -> str:
        """
        Generate the URL for the next page
        
        Args:
            current_url: Current page URL
            page_number: Next page number
            
        Returns:
            URL for the next page
        """
        # Handle category pages
        if self.category:
            return f"{self.BASE_URL}/category/{self.category}/page/{page_number}/"
        
        # Handle main site pagination
        return f"{self.BASE_URL}/page/{page_number}/"
    
    def scrape_multiple_pages(self, max_pages: int = 1) -> List[Dict[str, str]]:
        """
        Scrape articles from multiple pages
        
        Args:
            max_pages: Maximum number of pages to scrape
            
        Returns:
            List of article data dictionaries
        """
        all_articles = []
        
        for page_num in range(1, max_pages + 1):
            if page_num == 1:
                page_url = self.start_url
            else:
                page_url = self.get_next_page_url(self.start_url, page_num)
                
            logger.info(f"Scraping page {page_num}/{max_pages}: {page_url}")
            
            # Get article links from the page
            article_links = self.get_article_links(page_url)
            
            # Extract data from each article
            for link in article_links:
                article_data = self.extract_article_data(link)
                if article_data:
                    all_articles.append(article_data)
            
            # Don't try to get the next page if we didn't find any articles
            if not article_links and page_num > 1:
                logger.warning(f"No articles found on page {page_num}, stopping pagination")
                break
                
        return all_articles
    
    def save_to_json(self, articles: List[Dict[str, str]], filename: str) -> None:
        """
        Save articles to a JSON file
        
        Args:
            articles: List of article data dictionaries
            filename: Output filename
        """
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(articles, f, ensure_ascii=False, indent=2)
            
        logger.info(f"Saved {len(articles)} articles to {filename}")
    
    def save_to_csv(self, articles: List[Dict[str, str]], filename: str) -> None:
        """
        Save articles to a CSV file
        
        Args:
            articles: List of article data dictionaries
            filename: Output filename
        """
        if not articles:
            logger.warning("No articles to save")
            return
            
        # Get fieldnames from the first article
        fieldnames = articles[0].keys()
        
        with open(filename, 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            for article in articles:
                writer.writerow(article)
                
        logger.info(f"Saved {len(articles)} articles to {filename}")
