"""
Simple test script to verify Ollama is working correctly.
"""

import os
from langchain_ollama import ChatOllama
from langchain_ollama import OllamaEmbeddings
from document_loader import PrintScraperLoader

def test_ollama_chat():
    """Test basic Ollama chat functionality."""
    print("Testing Ollama chat...")
    try:
        chat_model = ChatOllama(model="llama3.2")
        response = chat_model.invoke("Hello, how are you?")
        print(f"Chat response: {response.content[:100]}...")
        print("Ollama chat test successful!")
        return True
    except Exception as e:
        print(f"Error testing Ollama chat: {e}")
        return False

def test_ollama_embeddings():
    """Test basic Ollama embeddings functionality."""
    print("\nTesting Ollama embeddings...")
    try:
        embeddings = OllamaEmbeddings(model="llama3.2")
        text = "This is a test sentence for embeddings."
        embedding = embeddings.embed_query(text)
        print(f"Embedding length: {len(embedding)}")
        print("Ollama embeddings test successful!")
        return True
    except Exception as e:
        print(f"Error testing Ollama embeddings: {e}")
        return False

def test_document_loading():
    """Test document loading functionality."""
    print("\nTesting document loading...")
    try:
        loader = PrintScraperLoader()
        documents = loader.load_documents(max_articles=2)
        print(f"Loaded {len(documents)} documents")
        if documents:
            print(f"First document title: {documents[0].metadata['title']}")
        print("Document loading test successful!")
        return True
    except Exception as e:
        print(f"Error testing document loading: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Ollama Test Script")
    print("=" * 50)
    
    # Test Ollama chat
    chat_success = test_ollama_chat()
    
    # Test Ollama embeddings
    embeddings_success = test_ollama_embeddings()
    
    # Test document loading
    docs_success = test_document_loading()
    
    # Print summary
    print("\n" + "=" * 50)
    print("Test Summary")
    print("=" * 50)
    print(f"Ollama Chat: {'✅ PASS' if chat_success else '❌ FAIL'}")
    print(f"Ollama Embeddings: {'✅ PASS' if embeddings_success else '❌ FAIL'}")
    print(f"Document Loading: {'✅ PASS' if docs_success else '❌ FAIL'}")
    
    if chat_success and embeddings_success and docs_success:
        print("\nAll tests passed! You can now run the full RAG agent.")
    else:
        print("\nSome tests failed. Please check the errors above.")
