import os
from typing import List, Dict, Any, Optional, Union
from dotenv import load_dotenv

from langchain_core.documents import Document
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.embeddings import Embeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS

# Ollama imports
from langchain_ollama import ChatOllama
from langchain_ollama import OllamaEmbeddings

# OpenAI imports
from langchain_openai import ChatOpenAI
from langchain_openai import OpenAIEmbeddings

from document_loader import PrintScraperLoader

# Load environment variables
load_dotenv()

class RAGAgent:
    """
    Retrieval-Augmented Generation (RAG) agent for answering questions based on PrintScraper data.
    """

    def __init__(self, model_name: str = None, vector_store_dir: str = "vector_store", use_openai: bool = False):
        """
        Initialize the RAG agent.

        Args:
            model_name: Name of the model to use (defaults to environment variable or model-specific default)
            vector_store_dir: Directory to save/load the vector store from
            use_openai: If True, use OpenAI models; if False, use Ollama models
        """
        # Set vector store directory
        self.vector_store_dir = vector_store_dir

        # Store model provider choice
        self.use_openai = use_openai

        # Initialize LLM and embeddings based on provider choice
        if use_openai:
            # Check if OpenAI API key is available
            if not os.getenv("OPENAI_API_KEY"):
                raise ValueError("OPENAI_API_KEY environment variable is not set")

            # Get model name from environment variable or use default
            if model_name is None:
                model_name = os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")

            # Initialize OpenAI components
            self.llm = ChatOpenAI(model_name=model_name)
            self.embeddings = OpenAIEmbeddings()
            print(f"Using OpenAI models: {model_name} for chat, text-embedding-ada-002 for embeddings")
        else:
            # Get model names from environment variables or use defaults
            if model_name is None:
                chat_model_name = os.getenv("OLLAMA_MODEL", "gemma4b")
            else:
                chat_model_name = model_name

            # Get embedding model name (can be different from chat model)
            embed_model_name = os.getenv("OLLAMA_EMBED_MODEL", chat_model_name)

            # Initialize Ollama components
            self.llm = ChatOllama(model=chat_model_name)
            self.embeddings = OllamaEmbeddings(model=embed_model_name)

            if chat_model_name == embed_model_name:
                print(f"Using Ollama model: {chat_model_name} for both chat and embeddings")
            else:
                print(f"Using Ollama models: {chat_model_name} for chat and {embed_model_name} for embeddings")

        # Initialize text splitter
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=5000,
            chunk_overlap=500
        )
        self.vector_store = None

        # Define the RAG prompt template
        self.prompt_template = ChatPromptTemplate.from_template("""
        You are an assistant that answers questions based on news articles from ThePrint.

        Answer the question based only on the following context:

        {context}

        Question: {question}

        Provide a comprehensive answer. If the context doesn't contain the information needed to answer the question,
        say "I don't have enough information to answer this question based on the available articles."

        Include relevant details from the articles such as dates, names, and specific facts when applicable.
        Cite the article titles in your response when referencing specific information.
        """)

    def load_and_index_documents(self, max_articles: int = None, force_reload: bool = False):
        """
        Load documents from PrintScraper output and index them for retrieval.

        Args:
            max_articles: Maximum number of articles to load (for testing purposes)
            force_reload: If True, regenerate the vector store even if it exists on disk
        """
        # Check if vector store already exists on disk
        vector_store_path = os.path.join(self.vector_store_dir, "faiss_index")
        if os.path.exists(vector_store_path) and not force_reload:
            print(f"Loading existing vector store from {vector_store_path}...")
            try:
                self.vector_store = FAISS.load_local(self.vector_store_dir, self.embeddings, "faiss_index")
                print("Vector store loaded successfully")
                return len(self.vector_store.index_to_docstore_id)
            except Exception as e:
                print(f"Error loading vector store: {e}")
                print("Will regenerate vector store...")

        # Create directory if it doesn't exist
        os.makedirs(self.vector_store_dir, exist_ok=True)

        # Load documents
        loader = PrintScraperLoader()
        documents = loader.load_documents(max_articles=max_articles)

        if not documents:
            raise ValueError("No documents loaded from PrintScraper output")

        # Split documents into chunks
        print("Splitting documents into chunks...")
        chunks = self.text_splitter.split_documents(documents)
        print(f"Created {len(chunks)} chunks from {len(documents)} documents")

        # Create vector store
        print("Creating vector store (this may take a while)...")
        if self.use_openai:
            print("Generating embeddings for chunks using OpenAI...")
        else:
            embed_model = os.getenv("OLLAMA_EMBED_MODEL", os.getenv("OLLAMA_MODEL", "bge-m3"))
            print(f"Generating embeddings for chunks using Ollama model {embed_model}...")
        self.vector_store = FAISS.from_documents(chunks, self.embeddings)

        # Save vector store to disk
        print(f"Saving vector store to {vector_store_path}...")
        self.vector_store.save_local(self.vector_store_dir, "faiss_index")
        print("Vector store created and saved successfully")

        return len(chunks)

    def answer_question(self, question: str, num_docs: int = 4) -> str:
        """
        Answer a question using RAG.

        Args:
            question: The question to answer
            num_docs: Number of documents to retrieve

        Returns:
            The answer to the question
        """
        if not self.vector_store:
            raise ValueError("Vector store not initialized. Call load_and_index_documents() first.")

        # Retrieve relevant documents
        retrieved_docs = self.vector_store.similarity_search(question, k=num_docs)

        # Format context from retrieved documents
        context = "\n\n".join([doc.page_content for doc in retrieved_docs])
        print("Context obtained from retrieved documents:")
        # print(context)

        # Create the prompt with context and question
        prompt = self.prompt_template.format(context=context, question=question)

        # Generate the answer
        response = self.llm.invoke(prompt)

        return response.content

    def get_document_sources(self, question: str, num_docs: int = 4) -> List[Dict[str, Any]]:
        """
        Get the sources of the documents used to answer a question.

        Args:
            question: The question to answer
            num_docs: Number of documents to retrieve

        Returns:
            List of document metadata
        """
        if not self.vector_store:
            raise ValueError("Vector store not initialized. Call load_and_index_documents() first.")

        # Retrieve relevant documents
        retrieved_docs = self.vector_store.similarity_search(question, k=num_docs)

        # Extract metadata
        sources = [doc.metadata for doc in retrieved_docs]

        return sources

if __name__ == "__main__":
    # Test the RAG agent
    agent = RAGAgent()

    try:
        num_chunks = agent.load_and_index_documents()
        print(f"Indexed {num_chunks} chunks")

        # Test question
        test_question = "What are the recent developments in Karnataka?"
        print(f"\nQuestion: {test_question}")

        answer = agent.answer_question(test_question)
        print(f"\nAnswer: {answer}")

        # Show sources
        sources = agent.get_document_sources(test_question)
        print("\nSources:")
        for i, source in enumerate(sources):
            print(f"{i+1}. {source['title']} by {source['author']} ({source['date']})")

    except Exception as e:
        print(f"Error: {e}")
