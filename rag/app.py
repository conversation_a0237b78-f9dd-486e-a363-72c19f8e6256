import os
import argparse
from dotenv import load_dotenv
from rag_agent import RAGAgent

# Load environment variables
load_dotenv()

def check_ollama_server():
    """Check if the Ollama server is running."""
    import requests
    try:
        response = requests.get("http://localhost:11434/api/version")
        if response.status_code == 200:
            return True
        else:
            print("Error: Ollama server is not responding correctly.")
            return False
    except requests.exceptions.ConnectionError:
        print("Error: Could not connect to Ollama server.")
        print("Please make sure Ollama is installed and running on your system.")
        print("Visit https://ollama.com/ for installation instructions.")
        return False

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="PrintScraper RAG Agent")
    parser.add_argument(
        "--force-reload",
        action="store_true",
        help="Force regeneration of the vector store"
    )
    parser.add_argument(
        "--max-articles",
        type=int,
        default=None,
        help="Maximum number of articles to load (default: all)"
    )
    parser.add_argument(
        "--use-openai",
        action="store_true",
        default=False,
        help="Use OpenAI models instead of Ollama (requires OPENAI_API_KEY)"
    )
    parser.add_argument(
        "--model",
        type=str,
        default=None,
        help="Model name to use for chat/completion (default depends on --use-openai flag)"
    )
    parser.add_argument(
        "--embed-model",
        type=str,
        default=None,
        help="Model name to use for embeddings (Ollama only, defaults to --model if not specified)"
    )
    return parser.parse_args()

def main():
    """Main function to run the RAG agent."""
    # Parse command-line arguments
    args = parse_args()

    print("=" * 50)
    print("PrintScraper RAG Agent")
    print("=" * 50)

    # Check if we need Ollama server
    if not args.use_openai:
        if not check_ollama_server():
            return
    elif not os.getenv("OPENAI_API_KEY"):
        print("Error: OPENAI_API_KEY environment variable is not set.")
        print("Please set your OpenAI API key as an environment variable or in a .env file.")
        print("Example: OPENAI_API_KEY=your-api-key-here")
        return

    # Initialize the RAG agent
    print("\nInitializing RAG agent...")

    # Set environment variable for embedding model if specified
    if args.embed_model and not args.use_openai:
        os.environ["OLLAMA_EMBED_MODEL"] = args.embed_model
        print(f"Using {args.embed_model} for embeddings")

    agent = RAGAgent(
        model_name=args.model,
        use_openai=args.use_openai
    )

    try:
        # Load and index documents
        print("\nLoading and indexing documents...")
        print("This process may take several minutes with Ollama, especially for the first run.")
        print("Please be patient while the documents are being processed...")
        # Use command-line arguments or defaults
        max_articles = args.max_articles  # From command-line or None (all articles)
        force_reload = args.force_reload  # From command-line (--force-reload flag)

        # Show what we're doing
        if max_articles is not None:
            print(f"Loading up to {max_articles} articles...")
        else:
            print("Loading all available articles...")

        if force_reload:
            print("Forcing regeneration of vector store...")

        # Load and index documents
        agent.load_and_index_documents(max_articles=max_articles, force_reload=force_reload)
        print("Documents loaded and indexed successfully.")

        # Main interaction loop
        print("\nRAG agent is ready! Type 'exit' to quit.")
        while True:
            # Get user question
            print("\n" + "-" * 50)
            question = input("Ask a question: ")

            # Exit if user types 'exit'
            if question.lower() in ["exit", "quit", "q"]:
                print("Exiting...")
                break

            # Answer the question
            print("\nGenerating answer...")
            answer = agent.answer_question(question)

            # Display the answer
            print("\nAnswer:")
            print(answer)

            # Display sources
            sources = agent.get_document_sources(question)
            print("\nSources:")
            for i, source in enumerate(sources):
                print(f"{i+1}. {source['title']} by {source['author']} ({source['date']})")

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
