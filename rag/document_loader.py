import os
import json
from typing import List, Dict, Any
from langchain_core.documents import Document

class PrintScraperLoader:
    """
    Loader for PrintScraper output files.
    Loads JSON files from the PrintScraper/output directory and converts them to Document objects.
    """

    def __init__(self, output_dir: str = "/Users/<USER>/Desktop/codes/lang/PrintScraper/output"):
        """
        Initialize the loader with the path to the PrintScraper output directory.

        Args:
            output_dir: Path to the PrintScraper output directory
        """
        self.output_dir = output_dir

    def load_documents(self, max_articles: int = None) -> List[Document]:
        """
        Load JSON files from the PrintScraper output directory and convert them to Document objects.

        Args:
            max_articles: Maximum number of articles to load (for testing purposes)

        Returns:
            List of Document objects
        """
        documents = []
        # Keep track of URLs to avoid duplicates
        seen_urls = set()
        # Counter for duplicates
        duplicate_count = 0

        # Get all JSON files in the output directory
        json_files = [f for f in os.listdir(self.output_dir) if f.endswith('.json')]

        for file_name in json_files:
            # Break if we've reached the maximum number of articles
            if max_articles is not None and len(documents) >= max_articles:
                break
            file_path = os.path.join(self.output_dir, file_name)

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    articles = json.load(f)

                # Convert each article to a Document object
                # Limit the number of articles if max_articles is specified
                article_count = 0
                for article in articles:
                    # Get the URL to check for duplicates
                    url = article.get("url", "")

                    # Skip if we've already seen this URL
                    if url and url in seen_urls:
                        duplicate_count += 1
                        continue

                    # Add URL to seen set if it exists
                    if url:
                        seen_urls.add(url)

                    # Create metadata from article fields
                    metadata = {
                        "title": article.get("title", ""),
                        "author": article.get("author", ""),
                        "date": article.get("date", ""),
                        "url": url,
                        "source": file_name
                    }

                    # Create document with content and metadata
                    # content = f"Title: {metadata['title']}\nAuthor: {metadata['author']}\nDate: {metadata['date']}\n\n{article.get('content', '')}"
                    content = f"{article.get('content', '')}"
                    doc = Document(page_content=content, metadata=metadata)
                    documents.append(doc)

                    # Increment article count and check if we've reached the limit
                    article_count += 1
                    if max_articles is not None and len(documents) >= max_articles:
                        print(f"Reached maximum of {max_articles} articles, stopping loading")
                        break

                print(f"Loaded {len(articles)} articles from {file_name}")

            except Exception as e:
                print(f"Error loading {file_path}: {e}")

        # Print summary including duplicate information
        print(f"Loaded {len(documents)} unique documents in total")
        if duplicate_count > 0:
            print(f"Skipped {duplicate_count} duplicate articles")
        return documents

if __name__ == "__main__":
    # Test the loader
    loader = PrintScraperLoader()
    documents = loader.load_documents()

    if documents:
        print("\nSample document:")
        print(f"Content (first 200 chars): {documents[0].page_content[:200]}...")
        print(f"Metadata: {documents[0].metadata}")
