# PrintScraper RAG Agent

A Retrieval-Augmented Generation (RAG) agent that answers questions based on news articles scraped by the PrintScraper tool.

## Overview

This RAG agent:
1. Loads scraped news articles from the PrintScraper/output directory
2. Indexes the articles for efficient retrieval
3. Answers questions based on the content of the articles
4. Provides sources for the information used to generate answers

## Requirements

- Python 3.8+
- Ollama installed and running (https://ollama.com/)
- llama3.2 model pulled in Ollama (`ollama pull llama3.2`)

### Installing Ollama

1. Visit [Ollama's website](https://ollama.com/) to download and install Ollama for your operating system.
2. After installation, start the Ollama service.
3. Pull the llama3.2 model by running: `ollama pull llama3.2`

## Installation

1. Navigate to the rag directory:
   ```bash
   cd rag
   ```

2. Install the required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up your environment (optional):
   - Copy the `.env.example` file to `.env`
   - You can customize the Ollama model in the `.env` file:
     ```
     OLLAMA_MODEL=llama3.2
     ```

## Usage

1. Make sure you have scraped data in the `PrintScraper/output` directory (one level up from the `rag` folder).

2. Run the RAG agent:
   ```bash
   cd rag
   python app.py
   ```

3. Ask questions about the news articles when prompted.

4. Type 'exit' to quit the application.

## Components

- `document_loader.py`: Loads and processes the scraped data from PrintScraper
- `rag_agent.py`: Implements the RAG pipeline (retrieval and generation)
- `app.py`: Provides a simple interface to interact with the RAG agent

## Example Questions

- "What are the recent developments in Karnataka?"
- "Tell me about the latest defense news in India."
- "What happened at the Karwar naval base?"
- "Who is TV Somanathan?"

## Command-line Options

The RAG agent supports the following command-line options:

```bash
python app.py [options]
```

Options:
- `--use-openai`: Use OpenAI models instead of Ollama (requires OPENAI_API_KEY)
- `--model MODEL`: Specify the model name to use
- `--max-articles N`: Limit the number of articles to load
- `--force-reload`: Force regeneration of the vector store

Examples:
```bash
# Use Ollama with default model (llama3.2)
python app.py

# Use OpenAI with default model (gpt-3.5-turbo)
python app.py --use-openai

# Use OpenAI with a specific model
python app.py --use-openai --model gpt-4

# Use Ollama with a specific model
python app.py --model llama3.1

# Limit to 50 articles and force vector store regeneration
python app.py --max-articles 50 --force-reload
```

## Customization

You can customize the RAG agent by modifying the following parameters in `rag_agent.py` or through environment variables:

- `model_name`: The model to use (default depends on provider)
- `chunk_size`: The size of text chunks for indexing (default: 5000)
- `chunk_overlap`: The overlap between text chunks (default: 500)
- `num_docs`: The number of documents to retrieve for each question (default: 4)
- `use_openai`: Whether to use OpenAI models (default: False)

### Model Configuration

#### Ollama Models
To use a different Ollama model:
1. Set the `OLLAMA_MODEL` environment variable
2. Update the `.env` file
3. Use the `--model` command-line option

#### OpenAI Models
To use OpenAI models:
1. Set the `OPENAI_API_KEY` environment variable
2. Set the `OPENAI_MODEL` environment variable (optional)
3. Use the `--use-openai` and optionally `--model` command-line options

## License

[MIT License](LICENSE)
