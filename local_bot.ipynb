{"cells": [{"cell_type": "code", "execution_count": 70, "id": "c8990403-4959-4caa-ac06-bf26224f02e9", "metadata": {}, "outputs": [], "source": ["from langchain_ollama import OllamaLLM\n", "from langchain_core.messages import HumanMessage, AIMessage\n", "from langchain_ollama import ChatOllama\n", "import os"]}, {"cell_type": "code", "execution_count": 12, "id": "4f2c7002-e5e4-4487-8070-2b584e87c705", "metadata": {}, "outputs": [], "source": ["llm = OllamaLLM(model=\"llama3.2\")"]}, {"cell_type": "code", "execution_count": 21, "id": "674e5956-3a8b-402c-8661-47fac3797978", "metadata": {}, "outputs": [], "source": ["os.environ['LANGSMITH_TRACING']=\"true\"\n", "os.environ['LANGSMITH_ENDPOINT']=\"https://api.smith.langchain.com\"\n", "os.environ['LANGSMITH_API_KEY']=\"***************************************************\"\n", "os.environ['LANGSMITH_PROJECT']=\"chatbot1\""]}, {"cell_type": "code", "execution_count": 22, "id": "6555ea8c-761f-4400-9f2e-64893575c2ec", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='<PERSON> was the first man to set foot on the Moon during the Apollo 11 mission in 1969. He stepped onto the lunar surface on July 20, 1969, famously declaring \"That\\'s one small step for man, one giant leap for mankind\" as he became the first human to do so.', additional_kwargs={}, response_metadata={'model': 'llama3.2', 'created_at': '2025-03-31T04:34:27.439875Z', 'done': True, 'done_reason': 'stop', 'total_duration': 2659637459, 'load_duration': 28638250, 'prompt_eval_count': 34, 'prompt_eval_duration': 924304792, 'eval_count': 66, 'eval_duration': 1705379292, 'message': Message(role='assistant', content='', images=None, tool_calls=None)}, id='run-71e727bd-9285-487d-b404-1a40012501f5-0', usage_metadata={'input_tokens': 34, 'output_tokens': 66, 'total_tokens': 100})"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["chat_model = ChatOllama(model=\"llama3.2\")\n", "chat_model.invoke(\"Who was the first man on the moon?\")"]}, {"cell_type": "code", "execution_count": 20, "id": "4ce0acc4-0d16-4714-8091-d4bcf8ff1466", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"Hello <PERSON>! It's nice to meet you. Is there something I can help you with, or would you like to chat?\", additional_kwargs={}, response_metadata={'model': 'llama3.2', 'created_at': '2025-03-31T04:33:44.021455Z', 'done': True, 'done_reason': 'stop', 'total_duration': 4012338959, 'load_duration': 40835542, 'prompt_eval_count': 30, 'prompt_eval_duration': 3286424750, 'eval_count': 27, 'eval_duration': 682138000, 'message': Message(role='assistant', content='', images=None, tool_calls=None)}, id='run-b0931e17-ddf7-462e-8568-28f9b1f510a3-0', usage_metadata={'input_tokens': 30, 'output_tokens': 27, 'total_tokens': 57})"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["chat_model.invoke([HumanMessage(content=\"Hi! I'm <PERSON>\")]) "]}, {"cell_type": "code", "execution_count": 16, "id": "2c7c62ff-fd58-425e-975f-b74af8c0fa4d", "metadata": {}, "outputs": [], "source": ["from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import START, MessagesState, StateGraph\n", "\n", "# Define a new graph\n", "workflow = StateGraph(state_schema=MessagesState)\n", "\n", "\n", "# Define the function that calls the model\n", "def call_model(state: MessagesState):\n", "    response = chat_model.invoke(state[\"messages\"])\n", "    return {\"messages\": response}\n", "\n", "\n", "# Define the (single) node in the graph\n", "workflow.add_edge(START, \"model\")\n", "workflow.add_node(\"model\", call_model)\n", "\n", "# Add memory\n", "memory = MemorySaver()\n", "app = workflow.compile(checkpointer=memory)"]}, {"cell_type": "code", "execution_count": 17, "id": "8784ac31-c504-4d00-8f03-deeadf622e72", "metadata": {}, "outputs": [], "source": ["config = {\"configurable\": {\"thread_id\": \"abc123\"}}"]}, {"cell_type": "code", "execution_count": 18, "id": "cc0ad306-1bea-464a-867b-2f42c144c50b", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Hi <PERSON>! It's nice to meet you. Is there something I can help you with, or would you like to chat?\n"]}], "source": ["query = \"Hi! I'm <PERSON>.\"\n", "\n", "input_messages = [HumanMessage(query)]\n", "output = app.invoke({\"messages\": input_messages}, config)\n", "output[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": 34, "id": "4dbeb667-85e2-4138-8300-6a597b3c277c", "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[34]\u001b[39m\u001b[32m, line 4\u001b[39m\n\u001b[32m      1\u001b[39m query = \u001b[33m\"\u001b[39m\u001b[33mWhat\u001b[39m\u001b[33m'\u001b[39m\u001b[33ms my name?\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m      3\u001b[39m input_messages = [HumanMessage(query)]\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m output = \u001b[43mapp\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43m{\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmessages\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43minput_messages\u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m      5\u001b[39m output[\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m][-\u001b[32m1\u001b[39m].pretty_print()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/langgraph/pregel/__init__.py:2683\u001b[39m, in \u001b[36mPregel.invoke\u001b[39m\u001b[34m(self, input, config, stream_mode, output_keys, interrupt_before, interrupt_after, debug, **kwargs)\u001b[39m\n\u001b[32m   2681\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   2682\u001b[39m     chunks = []\n\u001b[32m-> \u001b[39m\u001b[32m2683\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2684\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   2685\u001b[39m \u001b[43m    \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2686\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2687\u001b[39m \u001b[43m    \u001b[49m\u001b[43moutput_keys\u001b[49m\u001b[43m=\u001b[49m\u001b[43moutput_keys\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2688\u001b[39m \u001b[43m    \u001b[49m\u001b[43minterrupt_before\u001b[49m\u001b[43m=\u001b[49m\u001b[43minterrupt_before\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2689\u001b[39m \u001b[43m    \u001b[49m\u001b[43minterrupt_after\u001b[49m\u001b[43m=\u001b[49m\u001b[43minterrupt_after\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2690\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdebug\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdebug\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2691\u001b[39m \u001b[43m    \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2692\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m   2693\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m \u001b[49m\u001b[43m==\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mvalues\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\n\u001b[32m   2694\u001b[39m \u001b[43m        \u001b[49m\u001b[43mlatest\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/langgraph/pregel/__init__.py:2331\u001b[39m, in \u001b[36mPregel.stream\u001b[39m\u001b[34m(self, input, config, stream_mode, output_keys, interrupt_before, interrupt_after, debug, subgraphs)\u001b[39m\n\u001b[32m   2325\u001b[39m     \u001b[38;5;66;03m# Similarly to Bulk Synchronous Parallel / Pregel model\u001b[39;00m\n\u001b[32m   2326\u001b[39m     \u001b[38;5;66;03m# computation proceeds in steps, while there are channel updates.\u001b[39;00m\n\u001b[32m   2327\u001b[39m     \u001b[38;5;66;03m# Channel updates from step N are only visible in step N+1\u001b[39;00m\n\u001b[32m   2328\u001b[39m     \u001b[38;5;66;03m# channels are guaranteed to be immutable for the duration of the step,\u001b[39;00m\n\u001b[32m   2329\u001b[39m     \u001b[38;5;66;03m# with channel updates applied only at the transition between steps.\u001b[39;00m\n\u001b[32m   2330\u001b[39m     \u001b[38;5;28;01mwhile\u001b[39;00m loop.tick(input_keys=\u001b[38;5;28mself\u001b[39m.input_channels):\n\u001b[32m-> \u001b[39m\u001b[32m2331\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m_\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mrunner\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtick\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2332\u001b[39m \u001b[43m            \u001b[49m\u001b[43mloop\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtasks\u001b[49m\u001b[43m.\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2333\u001b[39m \u001b[43m            \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstep_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2334\u001b[39m \u001b[43m            \u001b[49m\u001b[43mretry_policy\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mretry_policy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2335\u001b[39m \u001b[43m            \u001b[49m\u001b[43mget_waiter\u001b[49m\u001b[43m=\u001b[49m\u001b[43mget_waiter\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2336\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m   2337\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;66;43;03m# emit output\u001b[39;49;00m\n\u001b[32m   2338\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01myield from\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43moutput\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   2339\u001b[39m \u001b[38;5;66;03m# emit output\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/langgraph/pregel/runner.py:146\u001b[39m, in \u001b[36mPregelRunner.tick\u001b[39m\u001b[34m(self, tasks, reraise, timeout, retry_policy, get_waiter)\u001b[39m\n\u001b[32m    144\u001b[39m t = tasks[\u001b[32m0\u001b[39m]\n\u001b[32m    145\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m146\u001b[39m     \u001b[43mrun_with_retry\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    147\u001b[39m \u001b[43m        \u001b[49m\u001b[43mt\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    148\u001b[39m \u001b[43m        \u001b[49m\u001b[43mretry_policy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    149\u001b[39m \u001b[43m        \u001b[49m\u001b[43mconfigurable\u001b[49m\u001b[43m=\u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m    150\u001b[39m \u001b[43m            \u001b[49m\u001b[43mCONFIG_KEY_CALL\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpartial\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    151\u001b[39m \u001b[43m                \u001b[49m\u001b[43m_call\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    152\u001b[39m \u001b[43m                \u001b[49m\u001b[43mweakref\u001b[49m\u001b[43m.\u001b[49m\u001b[43mref\u001b[49m\u001b[43m(\u001b[49m\u001b[43mt\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    153\u001b[39m \u001b[43m                \u001b[49m\u001b[43mretry\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretry_policy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    154\u001b[39m \u001b[43m                \u001b[49m\u001b[43mfutures\u001b[49m\u001b[43m=\u001b[49m\u001b[43mweakref\u001b[49m\u001b[43m.\u001b[49m\u001b[43mref\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfutures\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    155\u001b[39m \u001b[43m                \u001b[49m\u001b[43mschedule_task\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mschedule_task\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    156\u001b[39m \u001b[43m                \u001b[49m\u001b[43msubmit\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msubmit\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    157\u001b[39m \u001b[43m                \u001b[49m\u001b[43mreraise\u001b[49m\u001b[43m=\u001b[49m\u001b[43mreraise\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    158\u001b[39m \u001b[43m            \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    159\u001b[39m \u001b[43m        \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    160\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    161\u001b[39m     \u001b[38;5;28mself\u001b[39m.commit(t, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m    162\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/langgraph/pregel/retry.py:40\u001b[39m, in \u001b[36mrun_with_retry\u001b[39m\u001b[34m(task, retry_policy, configurable)\u001b[39m\n\u001b[32m     38\u001b[39m     task.writes.clear()\n\u001b[32m     39\u001b[39m     \u001b[38;5;66;03m# run the task\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m40\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mtask\u001b[49m\u001b[43m.\u001b[49m\u001b[43mproc\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtask\u001b[49m\u001b[43m.\u001b[49m\u001b[43minput\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     41\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m ParentCommand \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m     42\u001b[39m     ns: \u001b[38;5;28mstr\u001b[39m = config[CONF][CONFIG_KEY_CHECKPOINT_NS]\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/langgraph/utils/runnable.py:606\u001b[39m, in \u001b[36mRunnableSeq.invoke\u001b[39m\u001b[34m(self, input, config, **kwargs)\u001b[39m\n\u001b[32m    602\u001b[39m config = patch_config(\n\u001b[32m    603\u001b[39m     config, callbacks=run_manager.get_child(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mseq:step:\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mi\u001b[38;5;250m \u001b[39m+\u001b[38;5;250m \u001b[39m\u001b[32m1\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m    604\u001b[39m )\n\u001b[32m    605\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m i == \u001b[32m0\u001b[39m:\n\u001b[32m--> \u001b[39m\u001b[32m606\u001b[39m     \u001b[38;5;28minput\u001b[39m = \u001b[43mstep\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    607\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    608\u001b[39m     \u001b[38;5;28minput\u001b[39m = step.invoke(\u001b[38;5;28minput\u001b[39m, config)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/langgraph/utils/runnable.py:371\u001b[39m, in \u001b[36mRunnableCallable.invoke\u001b[39m\u001b[34m(self, input, config, **kwargs)\u001b[39m\n\u001b[32m    369\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    370\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m set_config_context(config) \u001b[38;5;28;01mas\u001b[39;00m context:\n\u001b[32m--> \u001b[39m\u001b[32m371\u001b[39m         ret = \u001b[43mcontext\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    372\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(ret, Run<PERSON><PERSON>) \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m.recurse:\n\u001b[32m    373\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m ret.invoke(\u001b[38;5;28minput\u001b[39m, config)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[32]\u001b[39m\u001b[32m, line 6\u001b[39m, in \u001b[36mcall_model\u001b[39m\u001b[34m(state)\u001b[39m\n\u001b[32m      4\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcall_model\u001b[39m(state: MessagesState):\n\u001b[32m      5\u001b[39m     prompt = prompt_template.invoke(state)\n\u001b[32m----> \u001b[39m\u001b[32m6\u001b[39m     response = \u001b[43mchat_model\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprompt\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m      7\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m {\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m: response}\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/langchain_core/language_models/chat_models.py:307\u001b[39m, in \u001b[36mBaseChatModel.invoke\u001b[39m\u001b[34m(self, input, config, stop, **kwargs)\u001b[39m\n\u001b[32m    296\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34minvoke\u001b[39m(\n\u001b[32m    297\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m    298\u001b[39m     \u001b[38;5;28minput\u001b[39m: LanguageModelInput,\n\u001b[32m   (...)\u001b[39m\u001b[32m    302\u001b[39m     **kwargs: Any,\n\u001b[32m    303\u001b[39m ) -> BaseMessage:\n\u001b[32m    304\u001b[39m     config = ensure_config(config)\n\u001b[32m    305\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(\n\u001b[32m    306\u001b[39m         ChatGeneration,\n\u001b[32m--> \u001b[39m\u001b[32m307\u001b[39m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgenerate_prompt\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    308\u001b[39m \u001b[43m            \u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_convert_input\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    309\u001b[39m \u001b[43m            \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    310\u001b[39m \u001b[43m            \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcallbacks\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    311\u001b[39m \u001b[43m            \u001b[49m\u001b[43mtags\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtags\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    312\u001b[39m \u001b[43m            \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmetadata\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    313\u001b[39m \u001b[43m            \u001b[49m\u001b[43mrun_name\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mrun_name\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    314\u001b[39m \u001b[43m            \u001b[49m\u001b[43mrun_id\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpop\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mrun_id\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    315\u001b[39m \u001b[43m            \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    316\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m.generations[\u001b[32m0\u001b[39m][\u001b[32m0\u001b[39m],\n\u001b[32m    317\u001b[39m     ).message\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/langchain_core/language_models/chat_models.py:843\u001b[39m, in \u001b[36mBaseChatModel.generate_prompt\u001b[39m\u001b[34m(self, prompts, stop, callbacks, **kwargs)\u001b[39m\n\u001b[32m    835\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mgenerate_prompt\u001b[39m(\n\u001b[32m    836\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m    837\u001b[39m     prompts: \u001b[38;5;28mlist\u001b[39m[PromptValue],\n\u001b[32m   (...)\u001b[39m\u001b[32m    840\u001b[39m     **kwargs: Any,\n\u001b[32m    841\u001b[39m ) -> LLMResult:\n\u001b[32m    842\u001b[39m     prompt_messages = [p.to_messages() \u001b[38;5;28;01mfor\u001b[39;00m p \u001b[38;5;129;01min\u001b[39;00m prompts]\n\u001b[32m--> \u001b[39m\u001b[32m843\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgenerate\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprompt_messages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/langchain_core/language_models/chat_models.py:683\u001b[39m, in \u001b[36mBaseChatModel.generate\u001b[39m\u001b[34m(self, messages, stop, callbacks, tags, metadata, run_name, run_id, **kwargs)\u001b[39m\n\u001b[32m    680\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m i, m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(messages):\n\u001b[32m    681\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    682\u001b[39m         results.append(\n\u001b[32m--> \u001b[39m\u001b[32m683\u001b[39m             \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_generate_with_cache\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    684\u001b[39m \u001b[43m                \u001b[49m\u001b[43mm\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    685\u001b[39m \u001b[43m                \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    686\u001b[39m \u001b[43m                \u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrun_managers\u001b[49m\u001b[43m[\u001b[49m\u001b[43mi\u001b[49m\u001b[43m]\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mrun_managers\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    687\u001b[39m \u001b[43m                \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    688\u001b[39m \u001b[43m            \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    689\u001b[39m         )\n\u001b[32m    690\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m    691\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m run_managers:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/langchain_core/language_models/chat_models.py:908\u001b[39m, in \u001b[36mBaseChatModel._generate_with_cache\u001b[39m\u001b[34m(self, messages, stop, run_manager, **kwargs)\u001b[39m\n\u001b[32m    906\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    907\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m inspect.signature(\u001b[38;5;28mself\u001b[39m._generate).parameters.get(\u001b[33m\"\u001b[39m\u001b[33mrun_manager\u001b[39m\u001b[33m\"\u001b[39m):\n\u001b[32m--> \u001b[39m\u001b[32m908\u001b[39m         result = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_generate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    909\u001b[39m \u001b[43m            \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\n\u001b[32m    910\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    911\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    912\u001b[39m         result = \u001b[38;5;28mself\u001b[39m._generate(messages, stop=stop, **kwargs)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/langchain_ollama/chat_models.py:705\u001b[39m, in \u001b[36mChatOllama._generate\u001b[39m\u001b[34m(self, messages, stop, run_manager, **kwargs)\u001b[39m\n\u001b[32m    698\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_generate\u001b[39m(\n\u001b[32m    699\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m    700\u001b[39m     messages: List[BaseMessage],\n\u001b[32m   (...)\u001b[39m\u001b[32m    703\u001b[39m     **kwargs: Any,\n\u001b[32m    704\u001b[39m ) -> ChatResult:\n\u001b[32m--> \u001b[39m\u001b[32m705\u001b[39m     final_chunk = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_chat_stream_with_aggregation\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    706\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mverbose\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mverbose\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\n\u001b[32m    707\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    708\u001b[39m     generation_info = final_chunk.generation_info\n\u001b[32m    709\u001b[39m     chat_generation = ChatGeneration(\n\u001b[32m    710\u001b[39m         message=AIMessage(\n\u001b[32m    711\u001b[39m             content=final_chunk.text,\n\u001b[32m   (...)\u001b[39m\u001b[32m    716\u001b[39m         generation_info=generation_info,\n\u001b[32m    717\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/langchain_ollama/chat_models.py:642\u001b[39m, in \u001b[36mChatOllama._chat_stream_with_aggregation\u001b[39m\u001b[34m(self, messages, stop, run_manager, verbose, **kwargs)\u001b[39m\n\u001b[32m    633\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_chat_stream_with_aggregation\u001b[39m(\n\u001b[32m    634\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m    635\u001b[39m     messages: List[BaseMessage],\n\u001b[32m   (...)\u001b[39m\u001b[32m    639\u001b[39m     **kwargs: Any,\n\u001b[32m    640\u001b[39m ) -> ChatGenerationChunk:\n\u001b[32m    641\u001b[39m     final_chunk = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m642\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_iterate_over_stream\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    643\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mfinal_chunk\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mis\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m:\u001b[49m\n\u001b[32m    644\u001b[39m \u001b[43m            \u001b[49m\u001b[43mfinal_chunk\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/langchain_ollama/chat_models.py:727\u001b[39m, in \u001b[36mChatOllama._iterate_over_stream\u001b[39m\u001b[34m(self, messages, stop, **kwargs)\u001b[39m\n\u001b[32m    720\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_iterate_over_stream\u001b[39m(\n\u001b[32m    721\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m    722\u001b[39m     messages: List[BaseMessage],\n\u001b[32m    723\u001b[39m     stop: Optional[List[\u001b[38;5;28mstr\u001b[39m]] = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m    724\u001b[39m     **kwargs: Any,\n\u001b[32m    725\u001b[39m ) -> Iterator[ChatGenerationChunk]:\n\u001b[32m    726\u001b[39m     is_thinking = \u001b[38;5;28;01m<PERSON><PERSON><PERSON>\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m727\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mstream_resp\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_create_chat_stream\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    728\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43misinstance\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mstream_resp\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mstr\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    729\u001b[39m \u001b[43m            \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mChatGenerationChunk\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    730\u001b[39m \u001b[43m                \u001b[49m\u001b[43mmessage\u001b[49m\u001b[43m=\u001b[49m\u001b[43mAIMessageChunk\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    731\u001b[39m \u001b[43m                    \u001b[49m\u001b[43mcontent\u001b[49m\u001b[43m=\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   (...)\u001b[39m\u001b[32m    744\u001b[39m \u001b[43m                \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    745\u001b[39m \u001b[43m            \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/langchain_ollama/chat_models.py:629\u001b[39m, in \u001b[36mChatOllama._create_chat_stream\u001b[39m\u001b[34m(self, messages, stop, **kwargs)\u001b[39m\n\u001b[32m    626\u001b[39m chat_params = \u001b[38;5;28mself\u001b[39m._chat_params(messages, stop, **kwargs)\n\u001b[32m    628\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m chat_params[\u001b[33m\"\u001b[39m\u001b[33mstream\u001b[39m\u001b[33m\"\u001b[39m]:\n\u001b[32m--> \u001b[39m\u001b[32m629\u001b[39m     \u001b[38;5;28;01<PERSON><PERSON> from\u001b[39;00m \u001b[38;5;28mself\u001b[39m._client.chat(**chat_params)\n\u001b[32m    630\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    631\u001b[39m     \u001b[38;5;28;01my<PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m._client.chat(**chat_params)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/ollama/_client.py:170\u001b[39m, in \u001b[36mClient._request.<locals>.inner\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m    167\u001b[39m   e.response.read()\n\u001b[32m    168\u001b[39m   \u001b[38;5;28;01mraise\u001b[39;00m ResponseError(e.response.text, e.response.status_code) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m170\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mline\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mr\u001b[49m\u001b[43m.\u001b[49m\u001b[43miter_lines\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    171\u001b[39m \u001b[43m  \u001b[49m\u001b[43mpart\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mjson\u001b[49m\u001b[43m.\u001b[49m\u001b[43mloads\u001b[49m\u001b[43m(\u001b[49m\u001b[43mline\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    172\u001b[39m \u001b[43m  \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43merr\u001b[49m\u001b[43m \u001b[49m\u001b[43m:=\u001b[49m\u001b[43m \u001b[49m\u001b[43mpart\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43merror\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/httpx/_models.py:929\u001b[39m, in \u001b[36mResponse.iter_lines\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    927\u001b[39m decoder = LineDecoder()\n\u001b[32m    928\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request=\u001b[38;5;28mself\u001b[39m._request):\n\u001b[32m--> \u001b[39m\u001b[32m929\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mtext\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43miter_text\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    930\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mline\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mdecoder\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdecode\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtext\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    931\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01myield\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mline\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/httpx/_models.py:916\u001b[39m, in \u001b[36mResponse.iter_text\u001b[39m\u001b[34m(self, chunk_size)\u001b[39m\n\u001b[32m    914\u001b[39m chunker = TextChunker(chunk_size=chunk_size)\n\u001b[32m    915\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request=\u001b[38;5;28mself\u001b[39m._request):\n\u001b[32m--> \u001b[39m\u001b[32m916\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mbyte_content\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43miter_bytes\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    917\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtext_content\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mdecoder\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdecode\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbyte_content\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    918\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunker\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdecode\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtext_content\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/httpx/_models.py:897\u001b[39m, in \u001b[36mResponse.iter_bytes\u001b[39m\u001b[34m(self, chunk_size)\u001b[39m\n\u001b[32m    895\u001b[39m chunker = ByteChunker(chunk_size=chunk_size)\n\u001b[32m    896\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request=\u001b[38;5;28mself\u001b[39m._request):\n\u001b[32m--> \u001b[39m\u001b[32m897\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mraw_bytes\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43miter_raw\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    898\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdecoded\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mdecoder\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdecode\u001b[49m\u001b[43m(\u001b[49m\u001b[43mraw_bytes\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    899\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunker\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdecode\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdecoded\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/httpx/_models.py:951\u001b[39m, in \u001b[36mResponse.iter_raw\u001b[39m\u001b[34m(self, chunk_size)\u001b[39m\n\u001b[32m    948\u001b[39m chunker = ByteChunker(chunk_size=chunk_size)\n\u001b[32m    950\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request=\u001b[38;5;28mself\u001b[39m._request):\n\u001b[32m--> \u001b[39m\u001b[32m951\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mraw_stream_bytes\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    952\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_num_bytes_downloaded\u001b[49m\u001b[43m \u001b[49m\u001b[43m+\u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mraw_stream_bytes\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    953\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunker\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdecode\u001b[49m\u001b[43m(\u001b[49m\u001b[43mraw_stream_bytes\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/httpx/_client.py:153\u001b[39m, in \u001b[36mBoundSyncStream.__iter__\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    152\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__iter__\u001b[39m(\u001b[38;5;28mself\u001b[39m) -> typing.Iterator[\u001b[38;5;28mbytes\u001b[39m]:\n\u001b[32m--> \u001b[39m\u001b[32m153\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_stream\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    154\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01<PERSON><PERSON>\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/httpx/_transports/default.py:127\u001b[39m, in \u001b[36mResponseStream.__iter__\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    125\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__iter__\u001b[39m(\u001b[38;5;28mself\u001b[39m) -> typing.Iterator[\u001b[38;5;28mbytes\u001b[39m]:\n\u001b[32m    126\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m map_httpcore_exceptions():\n\u001b[32m--> \u001b[39m\u001b[32m127\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mpart\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_httpcore_stream\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    128\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01<PERSON><PERSON>\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mpart\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/httpcore/_sync/connection_pool.py:407\u001b[39m, in \u001b[36mPoolByteStream.__iter__\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    405\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m    406\u001b[39m     \u001b[38;5;28mself\u001b[39m.close()\n\u001b[32m--> \u001b[39m\u001b[32m407\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exc \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/httpcore/_sync/connection_pool.py:403\u001b[39m, in \u001b[36mPoolByteStream.__iter__\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    401\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__iter__\u001b[39m(\u001b[38;5;28mself\u001b[39m) -> typing.Iterator[\u001b[38;5;28mbytes\u001b[39m]:\n\u001b[32m    402\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m403\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mpart\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_stream\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    404\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01myield\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mpart\u001b[49m\n\u001b[32m    405\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/httpcore/_sync/http11.py:342\u001b[39m, in \u001b[36mHTTP11ConnectionByteStream.__iter__\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    340\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m ShieldCancellation():\n\u001b[32m    341\u001b[39m     \u001b[38;5;28mself\u001b[39m.close()\n\u001b[32m--> \u001b[39m\u001b[32m342\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m exc\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/httpcore/_sync/http11.py:334\u001b[39m, in \u001b[36mHTTP11ConnectionByteStream.__iter__\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    332\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    333\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m Trace(\u001b[33m\"\u001b[39m\u001b[33mreceive_response_body\u001b[39m\u001b[33m\"\u001b[39m, logger, \u001b[38;5;28mself\u001b[39m._request, kwargs):\n\u001b[32m--> \u001b[39m\u001b[32m334\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_connection\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_receive_response_body\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    335\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01myield\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\n\u001b[32m    336\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m    337\u001b[39m     \u001b[38;5;66;03m# If we get an exception while streaming the response,\u001b[39;00m\n\u001b[32m    338\u001b[39m     \u001b[38;5;66;03m# we want to close the response (and possibly the connection)\u001b[39;00m\n\u001b[32m    339\u001b[39m     \u001b[38;5;66;03m# before raising that exception.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/httpcore/_sync/http11.py:203\u001b[39m, in \u001b[36mHTTP11Connection._receive_response_body\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    200\u001b[39m timeout = timeouts.get(\u001b[33m\"\u001b[39m\u001b[33mread\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m    202\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m203\u001b[39m     event = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_receive_event\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    204\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(event, h11.Data):\n\u001b[32m    205\u001b[39m         \u001b[38;5;28;01myield\u001b[39;00m \u001b[38;5;28mbytes\u001b[39m(event.data)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/httpcore/_sync/http11.py:217\u001b[39m, in \u001b[36mHTTP11Connection._receive_event\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m    214\u001b[39m     event = \u001b[38;5;28mself\u001b[39m._h11_state.next_event()\n\u001b[32m    216\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m event \u001b[38;5;129;01mis\u001b[39;00m h11.NEED_DATA:\n\u001b[32m--> \u001b[39m\u001b[32m217\u001b[39m     data = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_network_stream\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    218\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mREAD_NUM_BYTES\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\n\u001b[32m    219\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    221\u001b[39m     \u001b[38;5;66;03m# If we feed this case through h11 we'll raise an exception like:\u001b[39;00m\n\u001b[32m    222\u001b[39m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[32m    223\u001b[39m     \u001b[38;5;66;03m#     httpcore.RemoteProtocolError: can't handle event type\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    227\u001b[39m     \u001b[38;5;66;03m# perspective. Instead we handle this case distinctly and treat\u001b[39;00m\n\u001b[32m    228\u001b[39m     \u001b[38;5;66;03m# it as a ConnectError.\u001b[39;00m\n\u001b[32m    229\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m data == \u001b[33mb\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m._h11_state.their_state == h11.SEND_RESPONSE:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Desktop/codes/venvs/lang_stuff/lib/python3.13/site-packages/httpcore/_backends/sync.py:128\u001b[39m, in \u001b[36mSyncStream.read\u001b[39m\u001b[34m(self, max_bytes, timeout)\u001b[39m\n\u001b[32m    126\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m map_exceptions(exc_map):\n\u001b[32m    127\u001b[39m     \u001b[38;5;28mself\u001b[39m._sock.settimeout(timeout)\n\u001b[32m--> \u001b[39m\u001b[32m128\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_sock\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrecv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmax_bytes\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["query = \"What's my name?\"\n", "\n", "input_messages = [HumanMessage(query)]\n", "output = app.invoke({\"messages\": input_messages}, config)\n", "output[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": 51, "id": "8f154260-ce5a-4242-98a3-73f42585a835", "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\ntemplate = ChatPromptTemplate([\\n    (\"system\", \"You are a helpful AI bot.\"),\\n    # Means the template will receive an optional list of messages under\\n    # the \"conversation\" key\\n    (\"placeholder\", \"{conversation}\")\\n    # Equivalently:\\n    # MessagesPlaceholder(variable_name=\"conversation\", optional=True)\\n])\\n\\nprompt_value = template.invoke(\\n    {\\n        \"conversation\": [\\n            (\"human\", \"Hi!\"),\\n            (\"ai\", \"How can I assist you today?\"),\\n            (\"human\", \"Can you make me an ice cream sundae?\"),\\n            (\"ai\", \"No.\")\\n        ]\\n    }\\n)\\n'"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "\n", "prompt_template = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You talk like a pirate. Answer all questions to the best of your ability.\",\n", "        ),\n", "        MessagesPlaceholder(variable_name=\"messages\"),\n", "    ]\n", ")\n", "\n", "'''\n", "template = ChatPromptTemplate([\n", "    (\"system\", \"You are a helpful AI bot.\"),\n", "    # Means the template will receive an optional list of messages under\n", "    # the \"conversation\" key\n", "    (\"placeholder\", \"{conversation}\")\n", "    # Equivalently:\n", "    # MessagesPlaceholder(variable_name=\"conversation\", optional=True)\n", "])\n", "\n", "prompt_value = template.invoke(\n", "    {\n", "        \"conversation\": [\n", "            (\"human\", \"Hi!\"),\n", "            (\"ai\", \"How can I assist you today?\"),\n", "            (\"human\", \"Can you make me an ice cream sundae?\"),\n", "            (\"ai\", \"No.\")\n", "        ]\n", "    }\n", ")\n", "'''"]}, {"cell_type": "code", "execution_count": 54, "id": "ac34eb60-d32e-4e06-9004-e5f55d4e5e3e", "metadata": {}, "outputs": [], "source": ["workflow = StateGraph(state_schema=MessagesState)\n", "\n", "\n", "def call_model(state: MessagesState):\n", "    prompt = prompt_template.invoke(state)\n", "    response = chat_model.invoke(prompt)\n", "    return {\"messages\": response}\n", "\n", "\n", "workflow.add_edge(START, \"model\")\n", "workflow.add_node(\"model\", call_model)\n", "\n", "memory = MemorySaver()\n", "app = workflow.compile(checkpointer=memory)"]}, {"cell_type": "code", "execution_count": 62, "id": "414ca451-1a2f-431a-8c97-c7699831b48e", "metadata": {}, "outputs": [], "source": ["prompt_template = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a helpful assistant. Answer all questions to the best of your ability in {language}.\",\n", "        ),\n", "        MessagesPlaceholder(variable_name=\"messages\"),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 65, "id": "2111ca60-2280-4f43-8218-ac12bd261774", "metadata": {}, "outputs": [], "source": ["from typing import Sequence\n", "\n", "from langchain_core.messages import BaseMessage\n", "from langgraph.graph.message import add_messages\n", "from typing_extensions import Annotated, TypedDict\n", "\n", "\n", "class State(TypedDict):\n", "    messages: Annotated[Sequence[BaseMessage], add_messages]\n", "    language: str\n", "\n", "\n", "workflow = StateGraph(state_schema=State)\n", "\n", "\n", "def call_model(state: State):\n", "    prompt = prompt_template.invoke(state)\n", "    response = chat_model.invoke(prompt)\n", "    return {\"messages\": [response]}\n", "\n", "\n", "workflow.add_edge(START, \"model\")\n", "workflow.add_node(\"model\", call_model)\n", "\n", "memory = MemorySaver()\n", "app = workflow.compile(checkpointer=memory)"]}, {"cell_type": "code", "execution_count": 66, "id": "c2af15da-ac0e-470a-9ced-6750ec9fa1e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "<PERSON><PERSON>, ¡encantado de conocerte! ¿En qué puedo ayudarte hoy? (Hello <PERSON>, nice to meet you! What can I help you with today?)\n"]}], "source": ["config = {\"configurable\": {\"thread_id\": \"abc456\"}}\n", "query = \"Hi! I'm <PERSON>.\"\n", "language = \"Spanish\"\n", "\n", "input_messages = [HumanMessage(query)]\n", "output = app.invoke(\n", "    {\"messages\": input_messages, \"language\": language},\n", "    config,\n", ")\n", "output[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": 67, "id": "bd951ba5-1e4c-479b-bb2b-9fe3c807452a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Tu nombre es... (Your name is...) <PERSON>. ¡Eso es fácil! (That's easy!)\n"]}], "source": ["query = \"What is my name?\"\n", "\n", "input_messages = [HumanMessage(query)]\n", "output = app.invoke(\n", "    {\"messages\": input_messages},\n", "    config,\n", ")\n", "output[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": null, "id": "830ea111-6501-4f72-8cfc-b8f8a2d8ad6e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 75, "id": "a80d22c5-562b-4dfa-984c-27023de2d412", "metadata": {}, "outputs": [{"data": {"text/plain": ["[SystemMessage(content=\"you're a good assistant\", additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content=\"hi! I'm bob\", additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='hi!', additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='I like vanilla ice cream', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='nice', additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='whats 2 + 2', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='4', additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='thanks', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='no problem!', additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='having fun?', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='yes!', additional_kwargs={}, response_metadata={})]"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.messages import SystemMessage, trim_messages\n", "\n", "trimmer = trim_messages(\n", "    max_tokens=65,\n", "    strategy=\"last\",\n", "    token_counter=chat_model,\n", "    include_system=True,\n", "    allow_partial=False,\n", "    start_on=\"human\",\n", ")\n", "\n", "messages = [\n", "    SystemMessage(content=\"you're a good assistant\"),\n", "    HumanMessage(content=\"hi! I'm bob\"),\n", "    AIMessage(content=\"hi!\"),\n", "    HumanMessage(content=\"I like vanilla ice cream\"),\n", "    AIMessage(content=\"nice\"),\n", "    HumanMessage(content=\"whats 2 + 2\"),\n", "    AIMessage(content=\"4\"),\n", "    HumanMessage(content=\"thanks\"),\n", "    AIMessage(content=\"no problem!\"),\n", "    HumanMessage(content=\"having fun?\"),\n", "    AIMessage(content=\"yes!\"),\n", "]\n", "\n", "trimmer.invoke(messages)"]}, {"cell_type": "code", "execution_count": 77, "id": "72f7b9ac-89ce-45f9-9bd8-53470d7cac47", "metadata": {}, "outputs": [], "source": ["workflow = StateGraph(state_schema=State)\n", "\n", "\n", "def call_model(state: State):\n", "    trimmed_messages = trimmer.invoke(state[\"messages\"])\n", "    prompt = prompt_template.invoke(\n", "        {\"messages\": trimmed_messages, \"language\": state[\"language\"]}\n", "    )\n", "    response = chat_model.invoke(prompt)\n", "    return {\"messages\": [response]}\n", "\n", "\n", "workflow.add_edge(START, \"model\")\n", "workflow.add_node(\"model\", call_model)\n", "\n", "memory = MemorySaver()\n", "app = workflow.compile(checkpointer=memory)"]}, {"cell_type": "code", "execution_count": null, "id": "2faddff3-9cc5-4b43-8db6-87fe0febedf0", "metadata": {}, "outputs": [], "source": ["app.invoke()"]}], "metadata": {"kernelspec": {"display_name": "lang_stuff", "language": "python", "name": "lang_stuff"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}