#!/usr/bin/env python3
"""
Swiggy Data Scraper
A comprehensive web scraper for extracting restaurant data, menu items, and other information from Swiggy.com
"""

import json
import logging
import random
import time
import csv
import os
from datetime import datetime
from typing import Dict, List, Optional, Union
from urllib.parse import urljoin, urlparse, parse_qs

import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

# Configure logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SwiggyScraper:
    """Scraper for Swiggy.com restaurant and menu data"""
    
    BASE_URL = "https://www.swiggy.com"
    
    # Common user agents to rotate for requests
    USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.2 Safari/605.1.15",
        "Mozilla/5.0 (X11; Linux x86_64; rv:120.0) Gecko/20100101 Firefox/120.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
    ]
    
    def __init__(self, city: str = "bangalore", delay: float = 2.0, max_retries: int = 3, headless: bool = True):
        """
        Initialize the Swiggy scraper
        
        Args:
            city: The city to scrape data from (e.g., 'bangalore', 'mumbai', 'delhi')
            delay: Delay between requests in seconds
            max_retries: Maximum number of retry attempts for failed requests
            headless: Whether to run browser in headless mode
        """
        self.city = city.lower()
        self.delay = delay
        self.max_retries = max_retries
        self.headless = headless
        self.driver = None
        
        # Build the city URL
        self.city_url = f"{self.BASE_URL}/city/{self.city}"
        
        logger.info(f"Initialized Swiggy scraper for {city}")
        
    def setup_driver(self):
        """Initialize Selenium WebDriver"""
        try:
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument(f"--user-agent={random.choice(self.USER_AGENTS)}")
            
            # Disable images and CSS for faster loading
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.managed_default_content_settings.stylesheets": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.implicitly_wait(10)
            
            logger.info("Chrome WebDriver initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {e}")
            raise
    
    def close_driver(self):
        """Close the WebDriver"""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver closed")
    
    def _get_headers(self) -> Dict[str, str]:
        """Generate random headers for HTTP requests"""
        return {
            "User-Agent": random.choice(self.USER_AGENTS),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Cache-Control": "max-age=0",
        }
    
    def _make_request(self, url: str) -> Optional[requests.Response]:
        """Make an HTTP request with retry logic and rate limiting"""
        for attempt in range(self.max_retries):
            try:
                jitter = random.uniform(-0.5, 0.5)
                sleep_time = max(0.5, self.delay + jitter)
                
                if attempt > 0:
                    time.sleep(sleep_time * attempt)
                else:
                    time.sleep(sleep_time)
                
                logger.debug(f"Requesting URL: {url}")
                
                headers = self._get_headers()
                headers['Referer'] = self.BASE_URL
                
                session = requests.Session()
                session.headers.update(headers)
                
                response = session.get(url, timeout=30)
                
                if response.status_code == 200:
                    return response
                
                logger.warning(f"Request failed with status code {response.status_code}, attempt {attempt + 1}/{self.max_retries}")
                
            except requests.RequestException as e:
                logger.warning(f"Request error: {e}, attempt {attempt + 1}/{self.max_retries}")
                
            time.sleep(attempt * 2)
        
        logger.error(f"Failed to retrieve {url} after {self.max_retries} attempts")
        return None
    
    def get_restaurants_in_area(self, area: str, limit: int = 50) -> List[Dict[str, str]]:
        """
        Get restaurants in a specific area using Selenium
        
        Args:
            area: Area name (e.g., 'koramangala', 'indiranagar')
            limit: Maximum number of restaurants to fetch
            
        Returns:
            List of restaurant data dictionaries
        """
        if not self.driver:
            self.setup_driver()
        
        restaurants = []
        
        try:
            # Navigate to city page
            search_url = f"{self.city_url}"
            logger.info(f"Navigating to: {search_url}")
            self.driver.get(search_url)
            
            # Wait for page to load
            time.sleep(3)
            
            # Try to set location to the specified area
            try:
                # Look for location input or button
                location_elements = self.driver.find_elements(By.CSS_SELECTOR, 
                    "[data-testid='location-search-input'], input[placeholder*='location'], input[placeholder*='area']")
                
                if location_elements:
                    location_input = location_elements[0]
                    location_input.clear()
                    location_input.send_keys(area)
                    time.sleep(2)
                    
                    # Try to click on first suggestion
                    suggestions = self.driver.find_elements(By.CSS_SELECTOR, 
                        "[data-testid='location-suggestion'], .location-suggestion, .suggestion-item")
                    if suggestions:
                        suggestions[0].click()
                        time.sleep(3)
                        
            except Exception as e:
                logger.warning(f"Could not set location to {area}: {e}")
            
            # Scroll down to load more restaurants
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            scroll_count = 0
            max_scrolls = 10
            
            while scroll_count < max_scrolls and len(restaurants) < limit:
                # Scroll down
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                
                # Extract restaurant data from current view
                restaurant_elements = self.driver.find_elements(By.CSS_SELECTOR, 
                    "[data-testid='restaurant-card'], .restaurant-item, .RestaurantList__RestaurantAnchor-sc-1d3nl43-3")
                
                for element in restaurant_elements[len(restaurants):]:
                    if len(restaurants) >= limit:
                        break
                        
                    try:
                        restaurant_data = self._extract_restaurant_data_from_element(element)
                        if restaurant_data and restaurant_data not in restaurants:
                            restaurants.append(restaurant_data)
                            logger.info(f"Extracted restaurant: {restaurant_data.get('name', 'Unknown')}")
                            
                    except Exception as e:
                        logger.debug(f"Error extracting restaurant data: {e}")
                        continue
                
                # Check if we've reached the bottom
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    break
                last_height = new_height
                scroll_count += 1
                
        except Exception as e:
            logger.error(f"Error getting restaurants: {e}")
        
        logger.info(f"Extracted {len(restaurants)} restaurants from {area}")
        return restaurants
    
    def _extract_restaurant_data_from_element(self, element) -> Optional[Dict[str, str]]:
        """Extract restaurant data from a web element"""
        try:
            restaurant_data = {}
            
            # Restaurant name
            name_selectors = [
                "[data-testid='restaurant-name']",
                ".restaurant-name",
                ".RestaurantName",
                "h3", "h4", ".name"
            ]
            
            for selector in name_selectors:
                try:
                    name_element = element.find_element(By.CSS_SELECTOR, selector)
                    restaurant_data['name'] = name_element.text.strip()
                    break
                except:
                    continue
            
            # Rating
            rating_selectors = [
                "[data-testid='restaurant-rating']",
                ".rating",
                ".star-rating",
                ".RestaurantRating"
            ]
            
            for selector in rating_selectors:
                try:
                    rating_element = element.find_element(By.CSS_SELECTOR, selector)
                    restaurant_data['rating'] = rating_element.text.strip()
                    break
                except:
                    continue
            
            # Cuisine type
            cuisine_selectors = [
                "[data-testid='restaurant-cuisine']",
                ".cuisine",
                ".RestaurantCuisine",
                ".food-type"
            ]
            
            for selector in cuisine_selectors:
                try:
                    cuisine_element = element.find_element(By.CSS_SELECTOR, selector)
                    restaurant_data['cuisine'] = cuisine_element.text.strip()
                    break
                except:
                    continue
            
            # Delivery time
            time_selectors = [
                "[data-testid='delivery-time']",
                ".delivery-time",
                ".time",
                ".eta"
            ]
            
            for selector in time_selectors:
                try:
                    time_element = element.find_element(By.CSS_SELECTOR, selector)
                    restaurant_data['delivery_time'] = time_element.text.strip()
                    break
                except:
                    continue
            
            # Price for two / Cost
            price_selectors = [
                "[data-testid='price-for-two']",
                ".cost",
                ".price",
                ".RestaurantCost"
            ]
            
            for selector in price_selectors:
                try:
                    price_element = element.find_element(By.CSS_SELECTOR, selector)
                    restaurant_data['cost_for_two'] = price_element.text.strip()
                    break
                except:
                    continue
            
            # Restaurant URL
            try:
                link_element = element.find_element(By.TAG_NAME, "a")
                href = link_element.get_attribute("href")
                if href:
                    restaurant_data['url'] = href
            except:
                pass
            
            # Area/Location
            area_selectors = [
                "[data-testid='restaurant-area']",
                ".area",
                ".location",
                ".RestaurantArea"
            ]
            
            for selector in area_selectors:
                try:
                    area_element = element.find_element(By.CSS_SELECTOR, selector)
                    restaurant_data['area'] = area_element.text.strip()
                    break
                except:
                    continue
            
            # Only return if we have at least a name
            if restaurant_data.get('name'):
                return restaurant_data
                
        except Exception as e:
            logger.debug(f"Error extracting restaurant data: {e}")
        
        return None
    
    def get_menu_items(self, restaurant_url: str) -> List[Dict[str, str]]:
        """
        Get menu items from a specific restaurant
        
        Args:
            restaurant_url: URL of the restaurant
            
        Returns:
            List of menu item dictionaries
        """
        if not self.driver:
            self.setup_driver()
        
        menu_items = []
        
        try:
            logger.info(f"Getting menu from: {restaurant_url}")
            self.driver.get(restaurant_url)
            time.sleep(3)
            
            # Scroll to load menu items
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            scroll_count = 0
            max_scrolls = 5
            
            while scroll_count < max_scrolls:
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    break
                last_height = new_height
                scroll_count += 1
            
            # Extract menu items
            menu_selectors = [
                "[data-testid='menu-item']",
                ".menu-item",
                ".MenuItem",
                ".food-item"
            ]
            
            for selector in menu_selectors:
                try:
                    items = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if items:
                        for item in items:
                            menu_data = self._extract_menu_item_data(item)
                            if menu_data:
                                menu_items.append(menu_data)
                        break
                except:
                    continue
            
        except Exception as e:
            logger.error(f"Error getting menu items: {e}")
        
        logger.info(f"Extracted {len(menu_items)} menu items")
        return menu_items
    
    def _extract_menu_item_data(self, element) -> Optional[Dict[str, str]]:
        """Extract menu item data from a web element"""
        try:
            item_data = {}
            
            # Item name
            name_selectors = [
                "[data-testid='item-name']",
                ".item-name",
                ".MenuItem__Name",
                "h3", "h4"
            ]
            
            for selector in name_selectors:
                try:
                    name_element = element.find_element(By.CSS_SELECTOR, selector)
                    item_data['name'] = name_element.text.strip()
                    break
                except:
                    continue
            
            # Price
            price_selectors = [
                "[data-testid='item-price']",
                ".price",
                ".MenuItem__Price",
                ".cost"
            ]
            
            for selector in price_selectors:
                try:
                    price_element = element.find_element(By.CSS_SELECTOR, selector)
                    item_data['price'] = price_element.text.strip()
                    break
                except:
                    continue
            
            # Description
            desc_selectors = [
                "[data-testid='item-description']",
                ".description",
                ".MenuItem__Description",
                ".desc"
            ]
            
            for selector in desc_selectors:
                try:
                    desc_element = element.find_element(By.CSS_SELECTOR, selector)
                    item_data['description'] = desc_element.text.strip()
                    break
                except:
                    continue
            
            # Category
            category_selectors = [
                "[data-testid='item-category']",
                ".category",
                ".MenuItem__Category"
            ]
            
            for selector in category_selectors:
                try:
                    cat_element = element.find_element(By.CSS_SELECTOR, selector)
                    item_data['category'] = cat_element.text.strip()
                    break
                except:
                    continue
            
            if item_data.get('name'):
                return item_data
                
        except Exception as e:
            logger.debug(f"Error extracting menu item: {e}")
        
        return None
    
    def search_restaurants(self, query: str, area: str = "", limit: int = 20) -> List[Dict[str, str]]:
        """
        Search for restaurants by name or cuisine
        
        Args:
            query: Search query (restaurant name, cuisine type, etc.)
            area: Specific area to search in
            limit: Maximum number of results
            
        Returns:
            List of restaurant dictionaries
        """
        if not self.driver:
            self.setup_driver()
        
        restaurants = []
        
        try:
            # Navigate to search
            search_url = f"{self.city_url}/search"
            logger.info(f"Searching for '{query}' in {area or 'all areas'}")
            self.driver.get(search_url)
            time.sleep(3)
            
            # Enter search query
            search_selectors = [
                "[data-testid='search-input']",
                "input[placeholder*='Search']",
                ".search-input",
                "input[type='search']"
            ]
            
            for selector in search_selectors:
                try:
                    search_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    search_input.clear()
                    search_input.send_keys(query)
                    time.sleep(2)
                    
                    # Press enter or click search button
                    try:
                        search_button = self.driver.find_element(By.CSS_SELECTOR, 
                            "[data-testid='search-button'], .search-button, button[type='submit']")
                        search_button.click()
                    except:
                        search_input.send_keys("\n")
                    
                    time.sleep(3)
                    break
                    
                except:
                    continue
            
            # Extract search results
            restaurants = self.get_restaurants_in_area("", limit)
            
        except Exception as e:
            logger.error(f"Error searching restaurants: {e}")
        
        return restaurants
    
    def save_to_json(self, data: List[Dict], filename: str) -> None:
        """Save data to JSON file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"Data saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving to JSON: {e}")
    
    def save_to_csv(self, data: List[Dict], filename: str) -> None:
        """Save data to CSV file"""
        if not data:
            logger.warning("No data to save")
            return
        
        try:
            fieldnames = set()
            for item in data:
                fieldnames.update(item.keys())
            
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=list(fieldnames))
                writer.writeheader()
                writer.writerows(data)
            
            logger.info(f"Data saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")


def main():
    """Example usage of the Swiggy scraper"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Scrape restaurant data from Swiggy')
    parser.add_argument('--city', default='bangalore', help='City to scrape (default: bangalore)')
    parser.add_argument('--area', default='koramangala', help='Area to scrape (default: koramangala)')
    parser.add_argument('--limit', type=int, default=20, help='Number of restaurants to scrape (default: 20)')
    parser.add_argument('--search', help='Search for specific restaurants or cuisine')
    parser.add_argument('--format', choices=['json', 'csv'], default='json', help='Output format')
    parser.add_argument('--output', default='swiggy_data', help='Output file prefix')
    parser.add_argument('--headless', action='store_true', help='Run in headless mode')
    parser.add_argument('--menu', action='store_true', help='Also scrape menu items (slower)')
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs('output', exist_ok=True)
    
    # Initialize scraper
    scraper = SwiggyScraper(city=args.city, headless=args.headless)
    
    try:
        if args.search:
            # Search for specific restaurants
            restaurants = scraper.search_restaurants(args.search, args.area, args.limit)
        else:
            # Get restaurants in area
            restaurants = scraper.get_restaurants_in_area(args.area, args.limit)
        
        # Optionally get menu items
        if args.menu and restaurants:
            logger.info("Scraping menu items...")
            for restaurant in restaurants[:5]:  # Limit to first 5 for demo
                if restaurant.get('url'):
                    menu_items = scraper.get_menu_items(restaurant['url'])
                    restaurant['menu_items'] = menu_items
        
        # Save data
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"output/{args.output}_{args.city}_{args.area}_{timestamp}.{args.format}"
        
        if args.format == 'json':
            scraper.save_to_json(restaurants, filename)
        else:
            scraper.save_to_csv(restaurants, filename)
        
        print(f"\nScraping completed!")
        print(f"Found {len(restaurants)} restaurants")
        print(f"Data saved to: {filename}")
        
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
    except Exception as e:
        logger.error(f"Error during scraping: {e}")
    finally:
        scraper.close_driver()


if __name__ == "__main__":
    main()
