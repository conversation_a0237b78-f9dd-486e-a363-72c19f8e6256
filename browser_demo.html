<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #output {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
            min-height: 50px;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 10px;
            margin: 5px 0;
            background-color: #e3f2fd;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 Browser Capabilities Demo</h1>
        
        <div class="demo-section">
            <h2>Yes, I can use the browser!</h2>
            <p>I have access to a Puppeteer-controlled browser that allows me to:</p>
            <ul class="feature-list">
                <li>📄 Navigate to websites and local HTML files</li>
                <li>🖱️ Click on elements and buttons</li>
                <li>⌨️ Type text into input fields</li>
                <li>📸 Take screenshots of the current page</li>
                <li>📜 Scroll up and down pages</li>
                <li>🔍 Analyze page content and console logs</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>Interactive Demo</h2>
            <p>Click the buttons below to see JavaScript in action:</p>
            <button onclick="showMessage('Hello from the browser!')">Say Hello</button>
            <button onclick="showTime()">Show Current Time</button>
            <button onclick="changeBackground()">Change Background</button>
            <button onclick="clearOutput()">Clear Output</button>
            
            <div id="output">
                <p>Click a button to see the output here...</p>
            </div>
        </div>
    </div>

    <script>
        function showMessage(msg) {
            document.getElementById('output').innerHTML = `<p><strong>${msg}</strong></p>`;
            console.log('Message shown:', msg);
        }

        function showTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            document.getElementById('output').innerHTML = `<p>Current time: <strong>${timeString}</strong></p>`;
            console.log('Time displayed:', timeString);
        }

        function changeBackground() {
            const colors = ['#ffebee', '#e8f5e9', '#e3f2fd', '#fff3e0', '#f3e5f5'];
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            document.body.style.backgroundColor = randomColor;
            document.getElementById('output').innerHTML = `<p>Background changed to: <strong>${randomColor}</strong></p>`;
            console.log('Background changed to:', randomColor);
        }

        function clearOutput() {
            document.getElementById('output').innerHTML = '<p>Output cleared!</p>';
            console.log('Output cleared');
        }

        console.log('Browser demo page loaded successfully!');
    </script>
</body>
</html>
