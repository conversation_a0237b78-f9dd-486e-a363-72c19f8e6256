#!/usr/bin/env python3
"""
Swiggy Scraper Example Usage
This script demonstrates various ways to use the Swiggy scraper
"""

import os
import sys
from datetime import datetime

# Add the current directory to Python path to import our scraper
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from swiggy_scraper import Swiggy<PERSON>craper


def example_basic_scraping():
    """Example 1: Basic restaurant scraping in an area"""
    print("=" * 60)
    print("Example 1: Basic Restaurant Scraping")
    print("=" * 60)
    
    scraper = SwiggyScraper(city="bangalore", headless=True)
    
    try:
        # Get restaurants in Koramangala
        print("Scraping restaurants in Koramangala, Bangalore...")
        restaurants = scraper.get_restaurants_in_area("koramangala", limit=10)
        
        print(f"Found {len(restaurants)} restaurants")
        
        # Print first few restaurants
        for i, restaurant in enumerate(restaurants[:3], 1):
            print(f"\n{i}. {restaurant.get('name', 'Unknown')}")
            print(f"   Rating: {restaurant.get('rating', 'N/A')}")
            print(f"   Cuisine: {restaurant.get('cuisine', 'N/A')}")
            print(f"   Delivery Time: {restaurant.get('delivery_time', 'N/A')}")
            print(f"   Cost: {restaurant.get('cost_for_two', 'N/A')}")
        
        # Save data
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"output/example_basic_{timestamp}.json"
        os.makedirs('output', exist_ok=True)
        scraper.save_to_json(restaurants, filename)
        print(f"\nData saved to: {filename}")
        
    finally:
        scraper.close_driver()


def example_search_functionality():
    """Example 2: Search for specific restaurants or cuisine"""
    print("\n" + "=" * 60)
    print("Example 2: Search Functionality")
    print("=" * 60)
    
    scraper = SwiggyScraper(city="mumbai", headless=True)
    
    try:
        # Search for pizza places
        print("Searching for pizza restaurants in Mumbai...")
        pizza_restaurants = scraper.search_restaurants("pizza", limit=5)
        
        print(f"Found {len(pizza_restaurants)} pizza restaurants")
        
        for i, restaurant in enumerate(pizza_restaurants, 1):
            print(f"\n{i}. {restaurant.get('name', 'Unknown')}")
            print(f"   Rating: {restaurant.get('rating', 'N/A')}")
            print(f"   Area: {restaurant.get('area', 'N/A')}")
        
        # Save search results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"output/example_search_{timestamp}.json"
        os.makedirs('output', exist_ok=True)
        scraper.save_to_json(pizza_restaurants, filename)
        print(f"\nSearch results saved to: {filename}")
        
    finally:
        scraper.close_driver()


def example_menu_scraping():
    """Example 3: Get menu items from restaurants"""
    print("\n" + "=" * 60)
    print("Example 3: Menu Scraping")
    print("=" * 60)
    
    scraper = SwiggyScraper(city="delhi", headless=True)
    
    try:
        # Get a few restaurants first
        print("Getting restaurants in CP, Delhi...")
        restaurants = scraper.get_restaurants_in_area("connaught-place", limit=3)
        
        if not restaurants:
            print("No restaurants found. Trying different area...")
            restaurants = scraper.get_restaurants_in_area("", limit=3)
        
        # Get menu for the first restaurant with a URL
        for restaurant in restaurants:
            if restaurant.get('url'):
                print(f"\nGetting menu for: {restaurant.get('name', 'Unknown')}")
                
                menu_items = scraper.get_menu_items(restaurant['url'])
                restaurant['menu_items'] = menu_items
                
                print(f"Found {len(menu_items)} menu items")
                
                # Print first few menu items
                for i, item in enumerate(menu_items[:5], 1):
                    print(f"  {i}. {item.get('name', 'Unknown')} - {item.get('price', 'N/A')}")
                
                break  # Only get menu for first restaurant for demo
        
        # Save data with menu
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"output/example_menu_{timestamp}.json"
        os.makedirs('output', exist_ok=True)
        scraper.save_to_json(restaurants, filename)
        print(f"\nRestaurants with menu saved to: {filename}")
        
    finally:
        scraper.close_driver()


def example_multiple_cities():
    """Example 4: Scrape data from multiple cities"""
    print("\n" + "=" * 60)
    print("Example 4: Multiple Cities")
    print("=" * 60)
    
    cities_areas = [
        ("bangalore", "koramangala"),
        ("mumbai", "bandra"),
        ("delhi", "cp")
    ]
    
    all_data = {}
    
    for city, area in cities_areas:
        print(f"\nScraping {city.title()} - {area.title()}...")
        
        scraper = SwiggyScraper(city=city, headless=True)
        
        try:
            restaurants = scraper.get_restaurants_in_area(area, limit=5)
            all_data[f"{city}_{area}"] = restaurants
            print(f"Found {len(restaurants)} restaurants in {city} - {area}")
            
        finally:
            scraper.close_driver()
    
    # Save combined data
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"output/example_multi_city_{timestamp}.json"
    os.makedirs('output', exist_ok=True)
    
    # Use SwiggyScraper's save method (create temporary instance)
    temp_scraper = SwiggyScraper()
    temp_scraper.save_to_json(all_data, filename)
    print(f"\nMulti-city data saved to: {filename}")


def example_csv_output():
    """Example 5: Save data in CSV format"""
    print("\n" + "=" * 60)
    print("Example 5: CSV Output")
    print("=" * 60)
    
    scraper = SwiggyScraper(city="pune", headless=True)
    
    try:
        print("Scraping restaurants in Pune...")
        restaurants = scraper.get_restaurants_in_area("koregaon-park", limit=8)
        
        print(f"Found {len(restaurants)} restaurants")
        
        # Save as CSV
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"output/example_csv_{timestamp}.csv"
        json_filename = f"output/example_csv_{timestamp}.json"
        
        os.makedirs('output', exist_ok=True)
        
        scraper.save_to_csv(restaurants, csv_filename)
        scraper.save_to_json(restaurants, json_filename)
        
        print(f"\nData saved as:")
        print(f"CSV: {csv_filename}")
        print(f"JSON: {json_filename}")
        
    finally:
        scraper.close_driver()


def main():
    """Run all examples"""
    print("Swiggy Scraper Examples")
    print("=" * 60)
    print("This script will demonstrate various features of the Swiggy scraper.")
    print("Note: Each example runs independently and may take a few minutes.")
    print("=" * 60)
    
    # Create output directory
    os.makedirs('output', exist_ok=True)
    
    try:
        # Run examples
        example_basic_scraping()
        example_search_functionality()
        example_menu_scraping()
        example_multiple_cities()
        example_csv_output()
        
        print("\n" + "=" * 60)
        print("All examples completed successfully!")
        print("Check the 'output' directory for generated files.")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n\nExamples interrupted by user.")
    except Exception as e:
        print(f"\nError running examples: {e}")
        print("Make sure you have installed all requirements:")
        print("pip install -r swiggy_requirements.txt")


if __name__ == "__main__":
    main()
