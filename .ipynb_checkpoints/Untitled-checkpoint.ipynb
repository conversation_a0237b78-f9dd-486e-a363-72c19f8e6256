{"cells": [{"cell_type": "code", "execution_count": 9, "id": "4711b1fa-4692-4de9-8f82-8ee6f9087cad", "metadata": {}, "outputs": [], "source": ["from langchain_ollama import OllamaLLM\n", "from langchain_core.messages import HumanMessage, AIMessage\n", "from langchain_ollama import ChatOllama\n", "from langchain_ollama import OllamaEmbeddings\n", "from langchain_core.vectorstores import InMemoryVectorStore\n", "\n", "\n", "from langchain import hub\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_core.documents import Document\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "from langgraph.graph import START, StateGraph\n", "\n", "import os\n", "import bs4\n", "from typing_extensions import List, TypedDict"]}, {"cell_type": "code", "execution_count": 2, "id": "5d82eb78-8e09-41c1-9bc4-5a74161266c4", "metadata": {}, "outputs": [], "source": ["os.environ['LANGSMITH_TRACING']=\"true\"\n", "os.environ['LANGSMITH_ENDPOINT']=\"https://api.smith.langchain.com\"\n", "os.environ['LANGSMITH_API_KEY']=\"***************************************************\"\n", "os.environ['LANGSMITH_PROJECT']=\"rag1\""]}, {"cell_type": "code", "execution_count": 5, "id": "8e7d83cd-dc30-4b40-952e-0028499df09b", "metadata": {}, "outputs": [], "source": ["llm = OllamaLLM(model=\"llama3.2\")\n", "chat_model = ChatOllama(model=\"llama3.2\")\n", "embeddings = OllamaEmbeddings(model=\"llama3.2\")"]}, {"cell_type": "code", "execution_count": 6, "id": "1a69dc4f-4259-4be4-ac26-f770304064d6", "metadata": {}, "outputs": [], "source": ["vector_store = InMemoryVectorStore(embeddings)"]}, {"cell_type": "code", "execution_count": 10, "id": "e3861ae4-67f4-4aad-96eb-b79f243d69e9", "metadata": {}, "outputs": [], "source": ["loader = WebBaseLoader(\n", "    web_paths=(\"https://lilianweng.github.io/posts/2023-06-23-agent/\",),\n", "    bs_kwargs=dict(\n", "        parse_only=bs4.SoupStrainer(\n", "            class_=(\"post-content\", \"post-title\", \"post-header\")\n", "        )\n", "    ),\n", ")\n", "docs = loader.load()"]}, {"cell_type": "code", "execution_count": 11, "id": "c6faa14b-9ffc-424d-aa84-58dd88cb12e8", "metadata": {}, "outputs": [], "source": ["text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)\n", "all_splits = text_splitter.split_documents(docs)"]}, {"cell_type": "code", "execution_count": 14, "id": "f68d0161-3717-435f-a307-5a25ce8d791b", "metadata": {"scrolled": true}, "outputs": [], "source": ["_ = vector_store.add_documents(documents=all_splits)"]}, {"cell_type": "code", "execution_count": 23, "id": "91619b12-1ecc-4d90-ab9d-f32012e3abe6", "metadata": {}, "outputs": [], "source": ["class State(TypedDict):\n", "    question: str\n", "    context: List[Document]\n", "    answer: str\n", "\n", "\n", "# Define application steps\n", "def retrieve(state: State):\n", "    retrieved_docs = vector_store.similarity_search(state[\"question\"])\n", "    return {\"context\": retrieved_docs}\n", "\n", "\n", "def generate(state: State):\n", "    docs_content = \"\\n\\n\".join(doc.page_content for doc in state[\"context\"])\n", "    messages = prompt.invoke({\"question\": state[\"question\"], \"context\": docs_content})\n", "    response = llm.invoke(messages)\n", "    print(response)\n", "    return {\"answer\": response}"]}, {"cell_type": "code", "execution_count": 24, "id": "18cf2ddd-e1fb-4e44-8bdc-1a8676a8f161", "metadata": {}, "outputs": [], "source": ["prompt = hub.pull(\"rlm/rag-prompt\")\n", "\n", "graph_builder = StateGraph(State).add_sequence([retrieve, generate])\n", "graph_builder.add_edge(START, \"retrieve\")\n", "graph = graph_builder.compile()"]}, {"cell_type": "code", "execution_count": 25, "id": "ce392074-4530-434c-80c3-51ffac37bd05", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Task Decomposition is a technique where an agent breaks down complex tasks into smaller, simpler steps. This process allows models to utilize more computation and provide insights into their thinking process. The Chain of Thought (CoT) prompting technique is used for task decomposition, instructing the model to \"think step by step\".\n"]}], "source": ["response = graph.invoke({\"question\": \"What is Task Decomposition?\"})"]}, {"cell_type": "code", "execution_count": 2, "id": "c71675b6-b1a6-48d4-a206-9fd45d6f2ee0", "metadata": {}, "outputs": [], "source": ["\n", "x = '''\n", "So this opens up a huge possibility. This could be an extension of the Chinese economy,” he added.\n", "This highly offensive statement is not only insensitive and undiplomatic, it is deliberate interference in the internal matters of India. <PERSON><PERSON> does not hold an elected post in the interim government of Dhaka, nor is he competent to advise Beijing on how to conduct its economy in the region. His mandate, if any, is limited to the revival of the economy of Bangladesh and guiding it back on the path of democracy. The affairs in the country were derailed by an illegitimate uprising in 2024 by Islamic radicals under the guise of <PERSON><PERSON> (ICS), the student wing of Jamaat-e-Islami, aided and abated by the ISI of Pakistan.\n", "Enemies turn brothers\n", "'''"]}, {"cell_type": "code", "execution_count": 4, "id": "477de4fb-c854-45eb-884a-5ffca81dfe61", "metadata": {}, "outputs": [{"data": {"text/plain": ["760"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["len(x)"]}, {"cell_type": "code", "execution_count": 6, "id": "42caa3ca-32cd-41c1-a2ca-20ff01838e1e", "metadata": {}, "outputs": [], "source": ["import json\n", "d= json.load(open(\"PrintScraper/output/theprint_india_20250406_133109.json\"))"]}, {"cell_type": "code", "execution_count": 11, "id": "e6a5f721-1e7b-4ddb-8894-445e3b7c9c63", "metadata": {}, "outputs": [{"data": {"text/plain": ["5365"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["len(d[0]['content'])"]}, {"cell_type": "code", "execution_count": 32, "id": "519db5a9-e18a-4d89-bd7b-b7c07c44b922", "metadata": {}, "outputs": [], "source": ["import ollama\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 33, "id": "43846e98-8ce2-46ab-854a-d1d3d8456a7d", "metadata": {}, "outputs": [], "source": ["def sim_score(a,b):\n", "    return np.dot(a, b)/(np.linalg.norm(a)*np.linalg.norm(b))"]}, {"cell_type": "code", "execution_count": 60, "id": "7fe43af0-e7ab-4c2f-8566-527e8199c359", "metadata": {}, "outputs": [{"data": {"text/plain": ["'New Delhi: At 82, Sardar <PERSON><PERSON> may struggle to recall names, but he never forgets a face. He has been stitching military uniforms since he was 15.<PERSON>’s journey from the small village of Jagdev Kalan in Amritsar district to becoming a trusted name among India’s military personnel, including Field Marshal <PERSON> whose birth anniversary falls on 3 April, is one of resilience and dedication.<PERSON> arrived in Delhi at the age of 12, just after India gained independence. Poverty drove him to leave his village, with a large family to support and siblings to marry off. “I had completed my studies up to class 5 before moving here,” he recalled while speaking to ThePrint.He first stayed with an elder sister in Delhi Cantonment for three years before being placed under the care of <PERSON><PERSON><PERSON><PERSON> and <PERSON>rda<PERSON><PERSON> (93), family acquaintances from Punjab. At the age of 15, he began his apprenticeship under the <PERSON><PERSON> brothers, who taught him the trade. Their shop, now known as The Battle Fatigues Store, was earlier called Madras Cloth House and Popular Tailors. “They showed me the ropes, and my journey has been closely intertwined with theirs,” said <PERSON>.Over the years, <PERSON> has stitched uniforms for former Army chiefs, including Field Marshal <PERSON>, General <PERSON><PERSON><PERSON>, and General <PERSON><PERSON><PERSON>, who retired only last year.He recalled recognising most officers from their early years in service, even if he can’t remember their names.Given <PERSON>’s legacy, there are, however, others who claim to have stitched his uniforms in the same market. Only a few shops away sits a competitor by the name of Tailloo Ram, which, having opened in 1932, claims to be the oldest shop in the market. The owner of Tai<PERSON>o <PERSON> claims it was their shop which stitched General <PERSON>ekshaw’s clothing.<PERSON>flecting on his early struggles, <PERSON> said tailoring was more than a job—it was a necessity. “Work was a compulsion. There weren’t many opportunities in the village, and this trade had value, though it demanded effort,” he said. Tailoring in his early days involved intricate handwork, unlike today’s machine-aided methods. “Now, it’s easier, but back then, everything was done by hand.”His dedication hasn’t gone unnoticed. Many retired officers still visit him, including a general who came to the shop recently after decades. “The moment he saw me, his eyes lit up,” Singh recalled with pride.For Singh, it’s the relationships formed through his work that bring him joy. “Customers I have known for 30-50 years still come back. They have grown old, like me, but seeing each other brings happiness,” he said.He fondly remembered an air force pilot who likened their respective trades. “The pilot told me, ‘What aircraft is to us, the cloth you cut is to you. If you knew its value, you would never be able to cut it.’”Singh credits his mentor, Sardar Surjeet Singh Bawa, for his enduring success and continues to learn from him. “Even at 82, I consider myself a student. Tayaji, now 93, is still my guide,” he said.The family’s tailoring business has seen its share of glory. During the late 1990s and early 2000s, they stitched 1,500 uniforms daily for military units, including those deployed on UN missions. They also catered to production houses, with actor Shah Rukh Khan visiting the store during the filming of Fauji.Singh has five children—two daughters and three sons. While the daughters are married, the sons have pursued careers in electrical work, tailoring, and transportation. He never aspired for his children to join the military, saying, “They didn’t study enough for it.”(Edited by Radifah Kabir)Also Read: Complex network of India’s existing air defence capabilities & the way forward'"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["art['content'].replace(\"\\n\", \"\")"]}, {"cell_type": "code", "execution_count": 61, "id": "b10553bc-875a-4459-b516-1e8d14666551", "metadata": {}, "outputs": [], "source": ["vs = []\n", "for art in d:\n", "    vs.append(np.array(ollama.embed(model='llama3.2', input=art['content'].replace(\"\\n\", \"\")).embeddings))"]}, {"cell_type": "code", "execution_count": 30, "id": "b5b5adba-616c-44bc-95d3-c8b62d12e482", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 62, "id": "f8d7e976-51f7-4a57-8e2c-68c32ab1aaec", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'title': 'India, Pakistan are sleepwalking toward another crisis on the LOC',\n", " 'url': 'https://theprint.in/opinion/india-pakistan-are-sleepwalking-toward-another-crisis-on-the-loc/2579929/',\n", " 'author': '<PERSON><PERSON><PERSON>',\n", " 'date': '2025-04-06',\n", " 'content': 'Lieutenant-Colonel <PERSON><PERSON><PERSON> had tried to stall his incredulous headquarters when the orders came in to attack, claiming 13 December 1971 was an inauspicious date. The excuse had been manufactured on the fly, but it turned out to be prophetic. From an old mule shed on the mountain, ill-prepared soldiers desperately clawed their way up the path toward the summit at Daruchhian, their progress marked by blood-stained clods of earth. Five officers, seven junior commissioned officers, and 18 other soldiers were killed—with another 74 missing in action—before commanders called off the attack.\\nLast week, bitter fighting erupted again around that 1971 battleground. Troops positioned at the Indian Army posts—Nangi Tekri, Jungle Tekri and Bump, all captured in hand-to-hand combat during the lead-up to the battle at Darucchian—opened fire across the Line of Control in the most serious incident since a ceasefire was put in place in 2021.\\nThere is no clarity on exactly what happened. The Army rolled back its early claim that there had been an intrusion across the Line of Control but asserted that its troops had responded appropriately to a provocation. For its part, the Pakistan Army stated that it began a patrol on its side of the Line, which was hit by the accidental explosion of an old landmine, killing <PERSON><PERSON><PERSON><PERSON>.\\nLike so often in the past, India and Pakistan might just be walking toward another crisis on the LOC, oblivious of their missteps and misjudgments of the path.\\nFor weeks now, tensions have been mounting in the region as jihadists from Pakistan-occupied Kashmir have pushed their way past the LOC. Two Indian soldiers were killed near Akhnoor last month by an Improvised Explosive Device (IED) believed to have been planted on their patrol route by a jihadist unit. Islamabad, for its part, has alleged that the Indian Army has been planting IEDs targeting its soldiers and said two of its soldiers have been injured in gunfire.\\nEven violence in Kashmir has dipped, with killings last year falling to the lowest levels seen since 2012, Islamabad’s tone is hardening. “Three wars have been fought for Kashmir, and if ten more need to be fought, we will fight,” declared Pakistan’s army chief General Asim Munir at a Kashmir solidarity event in February. Last month, Indian diplomatic sources say, General Munir also told a closed-door session of Pakistan’s Parliamentary Committee on National Security that India was fuelling violence in Balochistan.\\nFrom Kargil to crisis management\\nLessons on the need to maintain peace on the LOC were learned by both India and Pakistan under the shadow of nuclear weapons. Following the two countries’ nuclear weapons tests in 1998, then-Army chief General Pervez Musharraf went to war in Kargil. General Musharraf believed—rightly, it turned out—that India would be deterred by the risk of a nuclear war from widening the conflict. Even though he lost in Kargil, he continued to ratchet up the pain. From 1999 to 2003, India lost a staggering 2,125 security force personnel in jihadist attacks—four times the number killed in Kargil.\\nThe Jaish-e-Muhammad attack on Parliament in 2001 convinced Prime Minister Atal Bihari Vajpayee that he needed to use coercive means to contain the threat. Vajpayee ordered the Army to position itself for war, leading to what has been described as the most significant military mobilisation since World War II. Then, in 2003, PM Vajpayee seemed to reverse course, announcing New Delhi was unilaterally “opening the door for talks”.\\nEven though experts have argued the 2001-2002 stand-off failed to secure its aims because of Pakistan’s nuclear deterrence—a proposition borne out of continued violence in Kashmir—the story is more complex. India did appear to blink first but General Musharraf’s advisors had concluded that the crisis was bleeding Pakistan more than it was hurting New Delhi.\\nLieutenant-General Moinuddin Haider, General Musharraf’s interior minister, told the scholar George Perkovich that he had explained to his boss: “Mr President, your economic plan will not work; people will not invest if you don’t get rid of extremists.” The President, albeit reluctantly, listened, violence data from Kashmir shows. Fatalities fell year-on-year as the Inter-Services Intelligence Directorate quietly choked off jihadist clients like the Jaish and Lashkar-e-Taiba.\\nUnsigned notes revealed in 2009 show that secret envoys working for Prime Minister Manmohan Singh and General Musharraf came close to a final-status deal on Kashmir. The negotiations, begun under Prime Minister Vajpayee by then-R&AW chief CD Sahay and ISI director Lieutenant-General Ehsan-ul-Haq, envisaged the LOC being turned into a border, with a high degree of freedom of movement and autonomy on both sides.\\nAlso read: Pakistan doesn’t have enough troops for 2 fronts. It has to choose between LOC, Balochistan\\nGeneral Kayani’s new war\\nHowever, the rise of General Pervez Ashfaq Kayani as Pakistan’s new army chief in 2008 saw this process unravel. In 2008, the US was reported to have confronted Pakistan’s army with evidence that the ISI was involved in a suicide attack on the Indian diplomatic mission in Kabul. Later that year came the carnage in Mumbai. Former Central Intelligence Agency chief Michael Hayden has recorded he was confident the ISI was involved in the planning and execution of the 2008 Mumbai attacks but failed to compel it to act against the perpetrators.\\nFor all practical purposes, General Kayani had dismantled the peace process that had started in the wake of the 2001-2002 crisis. Violence in Kashmir started to grow again. The LOC grew increasingly volatile as the Pakistan Army opened fire to facilitate infiltrating groups of jihadists. The two armies became locked in a cycle of increasingly brutal raids and counter-attacks, sometimes leading to the beheadings of soldiers.\\nThe killing of 17 Indian soldiers in Uri by the Lashkar-e-Taiba led Prime Minister Narendra Modi to order strikes across the Line of Control—an attack that sought to reimpose deterrence against Pakistan’s use of terrorism, but without locking India into a 2001-2002-like crisis. The damage caused by the strikes was minimal, but New Delhi hoped to show Islamabad it was willing to go to war if terrorism continued to escalate.\\nISI commanders were coerced into ending terrorist strikes outside Kashmir but stepped up violence within the state by using Fidayeen suicide attackers. This laid the foundation of three years of terrorism and Line of Control clashes, culminating in India’s bombing of a Jaish-e-Muhammad base in Pulwama.\\nBajwa’s long stalemate\\nFollowing the 2019 crisis, both sides pulled back from the edge. Like in 2003, R&AW and the ISI prepared the ground for negotiations, with Major-General Isfandiyar Pataudi and R Kumar meeting in secret at a London hotel. Few details have emerged on the negotiations, but their talks prepared the ground for the ceasefire in 2021. Like General Musharraf, General Qamar Javed Bajwa concluded that a crisis with India offered no real benefits to an economically devastated Pakistan and could only escalate into a full-blown war.\\nThe most crucial problem, though, is the absence of a constituency for India-Pakistan peace. As the scholar Christopher Clary has thoughtfully observed, political leaders remain wary of resuming talks. Modi and his cabinet have shown little interest in reopening dialogue. For his part, Prime Minister Shehbaz Sharif rejected even a limited reopening of trade with India in 2022, saying “genocide is going on there, and Kashmiris have been denied their rights.” Instead, Pakistani policymakers insist that dialogue must include Kashmir—something New Delhi has shot down.\\nEvents on the Line of Control last week, though, make clear that time isn’t on the side of either country. Even the limited terrorist attacks seen on the Indian Army and Hindu residents of Kashmir in 2024 could potentially have led to results that would have compelled New Delhi to strike across the border. The skirmishes on the Line of Control, similarly, can spiral, just like they did in the build-up after 1999, 2008, and 2016.\\nGeneral Munir’s words suggest he no longer sees a stalemate on the Line of Control as serving Pakistan’s interests. The calibrated use of violence might seem like leverage to him, but his predecessors learned that it’s only a small step from there to plunging the region into crisis.\\nPraveen Swami is contributing editor at ThePrint. His X handle is @praveenswami. Views are personal.\\n(Edited by Ratan Priya)\\nA tragic reminder that even instincts dismissed as superstition can echo the heavy cost of war.'}"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["query = \"story about <PERSON><PERSON><PERSON>\"\n", "query_emb = np.array(ollama.embed(model='llama3.2', input=query).embeddings)\n", "scores = []\n", "for v in vs:\n", "    scores.append(sim_score(query_emb.reshape(-1),v.reshape(-1)))\n", "scores = np.array(scores)\n", "d[np.a<PERSON><PERSON>(scores)]"]}, {"cell_type": "code", "execution_count": null, "id": "caf41f7d-b00a-432a-ad02-51705b4be191", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 51, "id": "9ada09fa-f107-4796-9cab-fb04fac511ff", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.21096967, 0.20615141, 0.21144065, 0.22011306, 0.21761578,\n", "       0.20769033, 0.21414354, 0.21466752, 0.22014009, 0.20732671,\n", "       0.20246263, 0.20755208, 0.21463491, 0.20823335, 0.24607524,\n", "       0.34409038, 0.42577998, 0.21096967, 0.20615141, 0.21144065,\n", "       0.22011306, 0.20732671, 0.20246263, 0.20755208, 0.20722607,\n", "       0.20510518, 0.21128924, 0.22715455, 0.20527507, 0.20305154,\n", "       0.21463491, 0.20823335, 0.24607524, 0.34409038, 0.42577998,\n", "       0.22011306, 0.22539511, 0.2094118 , 0.21910094, 0.20387287,\n", "       0.21792892, 0.21766109, 0.21922454, 0.21392692, 0.21244986,\n", "       0.22464316, 0.21557801, 0.21252145, 0.24607524, 0.25846693,\n", "       0.21003517, 0.34409038, 0.42577998, 0.22011306, 0.22539511,\n", "       0.2094118 , 0.21910094, 0.21043765, 0.21885459, 0.21911881,\n", "       0.2167125 , 0.2044851 , 0.21020788, 0.20346566, 0.2110469 ,\n", "       0.20314861, 0.2924174 , 0.21394856, 0.22736684, 0.34409038,\n", "       0.42577998, 0.22011306, 0.22539511, 0.2094118 , 0.21910094,\n", "       0.21869702, 0.21235635, 0.20061418, 0.20887377, 0.21043765,\n", "       0.21885459, 0.21911881, 0.2167125 , 0.2044851 , 0.21020788,\n", "       0.21003517, 0.2924174 , 0.21394856, 0.34409038, 0.42577998,\n", "       0.22011306, 0.22539511, 0.2094118 , 0.21910094, 0.20713155,\n", "       0.21419173, 0.19965277, 0.21837938, 0.20323476, 0.21672773,\n", "       0.21090932, 0.21973517, 0.20536028, 0.20818928, 0.2924174 ,\n", "       0.21394856, 0.22736684, 0.34409038, 0.42577998, 0.20615141,\n", "       0.21144065, 0.22011306, 0.22539511, 0.21700384, 0.2117185 ,\n", "       0.2164564 , 0.21869702, 0.21235635, 0.20061418, 0.20887377,\n", "       0.21043765, 0.21885459, 0.21911881, 0.21463491, 0.20823335,\n", "       0.24607524, 0.34409038, 0.42577998, 0.22011306, 0.22539511,\n", "       0.2094118 , 0.21910094, 0.21232525, 0.20713155, 0.21419173,\n", "       0.19965277, 0.21837938, 0.20323476, 0.21672773, 0.21090932,\n", "       0.21973517, 0.20536028, 0.24607524, 0.25846693, 0.21003517,\n", "       0.34409038, 0.42577998, 0.22011306, 0.22539511, 0.2094118 ,\n", "       0.21910094, 0.21993696, 0.20693015, 0.21495249, 0.21464399,\n", "       0.21540069, 0.20319825, 0.2157866 , 0.21697119, 0.21047129,\n", "       0.22061009, 0.2924174 , 0.21394856, 0.22736684, 0.34409038,\n", "       0.42577998, 0.22011306, 0.22539511, 0.2094118 , 0.21910094,\n", "       0.21221645, 0.22495662, 0.20900526, 0.21423491, 0.21207856,\n", "       0.20748307, 0.20601334, 0.20583882, 0.20950089, 0.2924174 ,\n", "       0.21394856, 0.22736684, 0.34409038, 0.42577998])"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["scores"]}, {"cell_type": "code", "execution_count": 50, "id": "e24a0ed3-0aad-4f6c-823a-ca260f5f4349", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(16)"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 52, "id": "15554a27-616e-4584-a0ae-6a72a706eda7", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.4257799827608562)"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 56, "id": "2a15030a-dd55-4e3f-a9a2-44c6008f92ce", "metadata": {}, "outputs": [], "source": ["import langchain_community"]}, {"cell_type": "code", "execution_count": 57, "id": "68db548c-130d-421a-83fd-e33b5a7d9cb5", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "module 'langchain_community' has no attribute 'graphs'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[57]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mlangchain_community\u001b[49m\u001b[43m.\u001b[49m\u001b[43mgraphs\u001b[49m.neo4j_graph(d[\u001b[32m16\u001b[39m][\u001b[33m'\u001b[39m\u001b[33mcontent\u001b[39m\u001b[33m'\u001b[39m])\n", "\u001b[31mAttributeError\u001b[39m: module 'langchain_community' has no attribute 'graphs'"]}], "source": ["langchain_community.graphs.neo4j_graph(d[16]['content'])"]}, {"cell_type": "code", "execution_count": 58, "id": "c4e36511-f9c3-45a8-9399-dbe7108a04f7", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "module 'langchain_community' has no attribute 'graphs'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[58]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mlangchain_community\u001b[49m\u001b[43m.\u001b[49m\u001b[43mgraphs\u001b[49m.neo4j_graph.clean_string_values(d[\u001b[32m16\u001b[39m])\n", "\u001b[31mAttributeError\u001b[39m: module 'langchain_community' has no attribute 'graphs'"]}], "source": ["langchain_community.graphs.neo4j_graph.clean_string_values(d[16])"]}, {"cell_type": "code", "execution_count": null, "id": "f810ed80-7902-4f30-88ed-c6939962dcbd", "metadata": {}, "outputs": [], "source": ["langchain_community.clean"]}], "metadata": {"kernelspec": {"display_name": "lang_stuff", "language": "python", "name": "lang_stuff"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}